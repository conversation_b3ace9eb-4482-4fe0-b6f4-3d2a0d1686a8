"""
PRODUCTION-READY PURE MARKET MAKING STRATEGY WITH BUY-LOW/SELL-HIGH ENFORCEMENT

CRITICAL SAFETY FEATURES FOR PRODUCTION USE:
============================================

1. BUY-LOW/SELL-HIGH ENFORCEMENT:
   - Analyzes price trends over multiple time windows (5min, 15min, 30min)
   - Sets dynamic buy ceiling and sell floor based on recent price action
   - Rejects orders that would buy high or sell low
   - Prevents systematic losses in trending markets

2. TREND-AWARE TRADING BOUNDARIES:
   - UPTREND: Conservative buys (below recent average), aggressive sells
   - DOWNTREND: Aggressive buys (below recent high), conservative sells
   - NEUTRAL: Balanced approach with volatility-based buffers

3. PROFITABILITY PROTECTION:
   - Ensures minimum profit margin (default 0.2%) on all trades
   - Validates that buy orders can be profitably sold above safe sell floor
   - Checks sell orders against recent buy prices for profitability

4. COMPREHENSIVE LOGGING & MONITORING:
   - Detailed rejection logging for analysis
   - Trend analysis and boundary updates
   - Safety statistics and warnings

CONFIGURATION PARAMETERS:
========================
- enforce_buy_low_sell_high: Master switch (default: True)
- min_profit_margin: Minimum profit required (default: 0.2%)
- price_trend_threshold: Trend detection sensitivity (default: 1%)
- price_memory_window: Price history window (default: 1 hour)

This system is designed to prevent the systematic losses that can occur
in production environments with millions of dollars at stake.
"""

import logging
import random
import time
from collections import deque
from decimal import Decimal, ROUND_DOWN
from math import ceil, floor
from typing import Dict, List, Optional, Tuple

import numpy as np
import pandas as pd

from hummingbot.connector.exchange_base import ExchangeBase
from hummingbot.connector.exchange_base cimport ExchangeBase
from hummingbot.core.clock cimport Clock
from hummingbot.core.data_type.common import OrderType, PriceType, TradeType
from hummingbot.core.data_type.limit_order cimport LimitOrder
from hummingbot.core.data_type.limit_order import LimitOrder
from hummingbot.core.network_iterator import NetworkStatus
from hummingbot.core.utils import map_df_to_str
from hummingbot.strategy.asset_price_delegate cimport AssetPriceDelegate
from hummingbot.strategy.asset_price_delegate import AssetPriceDelegate
from hummingbot.strategy.hanging_orders_tracker import CreatedPairOfOrders, HangingOrdersTracker
from hummingbot.strategy.market_trading_pair_tuple import MarketTradingPairTuple
from hummingbot.strategy.order_book_asset_price_delegate cimport OrderBookAssetPriceDelegate
from hummingbot.strategy.strategy_base import StrategyBase
from hummingbot.strategy.utils import order_age
from .data_types import PriceSize, Proposal
from .inventory_cost_price_delegate import InventoryCostPriceDelegate
from .inventory_skew_calculator cimport c_calculate_bid_ask_ratios_from_base_asset_ratio
from .inventory_skew_calculator import calculate_total_order_size
from .pure_market_making_order_tracker import PureMarketMakingOrderTracker
from .moving_price_band import MovingPriceBand


NaN = float("nan")
MINUTE = 60
s_decimal_zero = Decimal(0)
s_decimal_neg_one = Decimal(-1)
pmm_logger = None


cdef class PureMarketMakingStrategy(StrategyBase):
    OPTION_LOG_CREATE_ORDER = 1 << 3
    OPTION_LOG_MAKER_ORDER_FILLED = 1 << 4
    OPTION_LOG_STATUS_REPORT = 1 << 5
    OPTION_LOG_ALL = 0x7fffffffffffffff

    @classmethod
    def logger(cls):
        global pmm_logger
        if pmm_logger is None:
            pmm_logger = logging.getLogger(__name__)
        return pmm_logger

    def init_params(self,
                    market_info: MarketTradingPairTuple,
                    bid_spread: Decimal,
                    ask_spread: Decimal,
                    order_amount: Decimal,
                    order_levels: int = 1,
                    order_level_spread: Decimal = s_decimal_zero,
                    order_level_amount: Decimal = s_decimal_zero,
                    order_refresh_time_min: float = 5.0,
                    order_refresh_time_max: float = 15.0,
                    max_order_age: float = 1800.0,
                    order_refresh_tolerance_pct: Decimal = s_decimal_zero,
                    filled_order_delay: float = 60.0,
                    inventory_skew_enabled: bool = False,
                    inventory_target_base_pct: Decimal = s_decimal_zero,
                    inventory_range_multiplier: Decimal = s_decimal_zero,
                    hanging_orders_enabled: bool = False,
                    hanging_orders_cancel_pct: Decimal = Decimal("0.1"),
                    order_optimization_enabled: bool = False,
                    ask_order_optimization_depth: Decimal = s_decimal_zero,
                    bid_order_optimization_depth: Decimal = s_decimal_zero,
                    add_transaction_costs_to_orders: bool = False,
                    asset_price_delegate: AssetPriceDelegate = None,
                    inventory_cost_price_delegate: InventoryCostPriceDelegate = None,
                    price_type: str = "mid_price",
                    take_if_crossed: bool = False,
                    price_ceiling: Decimal = s_decimal_neg_one,
                    price_floor: Decimal = s_decimal_neg_one,
                    ping_pong_enabled: bool = False,
                    logging_options: int = OPTION_LOG_ALL,
                    status_report_interval: float = 900,
                    minimum_spread: Decimal = Decimal(0),
                    hb_app_notification: bool = False,
                    order_override: Dict[str, List[str]] = None,
                    split_order_levels_enabled: bool = False,
                    bid_order_level_spreads: List[Decimal] = None,
                    ask_order_level_spreads: List[Decimal] = None,
                    bid_order_level_amounts: List[Decimal] = None,
                    ask_order_level_amounts: List[Decimal] = None,


                    should_wait_order_cancel_confirmation: bool = True,
                    moving_price_band: Optional[MovingPriceBand] = None,
                    enable_spread_adjustment: bool = True,
                    max_buy_sell_ratio: Decimal = Decimal("0.98"),
                    spread_adjustment_increment: Decimal = Decimal("0.1"),
                    min_allowed_spread: Decimal = Decimal("0.05"),
                    price_ratio_log_level: str = "WARNING",

                    # PRODUCTION SAFETY: Buy-Low/Sell-High Enforcement Parameters
                    enforce_buy_low_sell_high: bool = True,
                    min_profit_margin: Decimal = Decimal("0.002"),
                    price_trend_threshold: Decimal = Decimal("0.01"),
                    price_memory_window: float = 3600.0,

                    # DEX COORDINATION: Cross-platform coordination parameters
                    enable_dex_coordination: bool = False,
                    dex_chain: str = "base",
                    dex_network: str = "mainnet",
                    dex_connector: str = "uniswap",
                    dex_base_token_address: Optional[str] = None,
                    dex_quote_token_address: str = "0x4200000000000000000000000000000000000006",
                    dex_pool_fee_tier: int = 3000,
                    dex_pool_address: Optional[str] = None,
                    dex_price_update_interval: float = 30.0,  # Increased for rate limiting
                    dex_price_staleness_threshold: float = 90.0,  # Increased for rate limiting
                    dex_subgraph_bearer_token: Optional[str] = None,  # Bearer token for authenticated access
                    dex_subgraph_rate_limit_seconds: int = 30,  # Rate limit for subgraph requests
                    enable_arbitrage_protection: bool = True,
                    max_price_deviation_pct: Decimal = Decimal("2.0"),
                    arbitrage_protection_spread_adjustment: Decimal = Decimal("0.5"),
                    arbitrage_defense_mode: str = "normal",
                    enable_dex_liquidity_adjustment: bool = True,
                    min_dex_liquidity_threshold: Decimal = Decimal("10000.0"),
                    low_liquidity_spread_multiplier: Decimal = Decimal("1.5"),
                    enable_order_coordination: bool = False,
                    coordination_delay_seconds: float = 2.0,
                    max_coordination_attempts: int = 3,
                    enable_cross_platform_risk_limits: bool = True,
                    max_daily_arbitrage_exposure: Decimal = Decimal("5000.0"),
                    emergency_stop_price_deviation: Decimal = Decimal("5.0"),

                    # ±2% DEPTH OPTIMIZATION: Parameters for maximizing liquidity depth
                    enable_depth_optimization: bool = False,
                    depth_optimization_range_pct: Decimal = Decimal("2.0"),
                    depth_budget_allocation_pct: Decimal = Decimal("80.0"),
                    min_depth_order_size: Decimal = Decimal("50.0"),
                    enable_dynamic_depth_adjustment: bool = False,
                    volatility_depth_multiplier: Decimal = Decimal("1.2"),
                    target_cumulative_depth_usd: Decimal = Decimal("15000.0"),

                    # OWN-TOKEN SAFETY: Parameters for own-token market making
                    own_token_market_making: bool = False
                    ):
        if order_override is None:
            order_override = {}
        if moving_price_band is None:
            moving_price_band = MovingPriceBand()
        if price_ceiling != s_decimal_neg_one and price_ceiling < price_floor:
            raise ValueError("Parameter price_ceiling cannot be lower than price_floor.")
        self._sb_order_tracker = PureMarketMakingOrderTracker()
        self._market_info = market_info
        self._bid_spread = bid_spread
        self._ask_spread = ask_spread
        self._original_bid_spread = bid_spread  # Store original spreads for reference
        self._original_ask_spread = ask_spread
        self._minimum_spread = minimum_spread
        self._order_amount = order_amount
        self._optimal_order_size = order_amount  # Initialize with base order amount
        self._order_levels = order_levels
        self._buy_levels = order_levels
        self._sell_levels = order_levels
        self._order_level_spread = order_level_spread
        self._order_level_amount = order_level_amount
        self._order_refresh_time_min = order_refresh_time_min
        self._order_refresh_time_max = order_refresh_time_max
        self._max_order_age = max_order_age
        self._order_refresh_tolerance_pct = order_refresh_tolerance_pct
        self._filled_order_delay = filled_order_delay
        self._inventory_skew_enabled = inventory_skew_enabled
        self._inventory_target_base_pct = inventory_target_base_pct
        self._inventory_range_multiplier = inventory_range_multiplier
        self._hanging_orders_enabled = hanging_orders_enabled
        from hummingbot.strategy.hanging_orders_tracker import HangingOrdersTracker
        self._hanging_orders_tracker = HangingOrdersTracker(self, hanging_orders_cancel_pct)
        self._order_optimization_enabled = order_optimization_enabled
        self._ask_order_optimization_depth = ask_order_optimization_depth
        self._bid_order_optimization_depth = bid_order_optimization_depth
        self._add_transaction_costs_to_orders = add_transaction_costs_to_orders
        self._asset_price_delegate = asset_price_delegate
        self._inventory_cost_price_delegate = inventory_cost_price_delegate
        self._price_type = self.get_price_type(price_type)
        self._take_if_crossed = take_if_crossed
        self._price_ceiling = price_ceiling
        self._price_floor = price_floor
        self._ping_pong_enabled = ping_pong_enabled
        self._ping_pong_warning_lines = []
        self._hb_app_notification = hb_app_notification
        self._order_override = order_override
        self._split_order_levels_enabled = split_order_levels_enabled
        self._bid_order_level_spreads = bid_order_level_spreads
        self._ask_order_level_spreads = ask_order_level_spreads
        self._bid_order_level_amounts = bid_order_level_amounts
        self._ask_order_level_amounts = ask_order_level_amounts
        self._cancel_timestamp = 0
        self._create_timestamp = 0
        self._limit_order_type = self._market_info.market.get_maker_order_type()
        if take_if_crossed:
            self._limit_order_type = OrderType.LIMIT

        # Price ratio monitoring configuration
        self._enable_spread_adjustment = enable_spread_adjustment
        self._original_bid_spread = bid_spread  # Store original spreads for reference
        self._original_ask_spread = ask_spread
        self._max_buy_sell_ratio = max_buy_sell_ratio
        self._spread_adjustment_increment = spread_adjustment_increment
        self._min_allowed_spread = min_allowed_spread
        self._price_ratio_log_level = price_ratio_log_level.upper()
        self._last_ratio_warning = 0  # Timestamp of last ratio warning
        self._last_ratio_log = 0  # Timestamp of last ratio log
        self._ratio_warning_interval = 300  # 5 minutes between ratio warnings

        # Initialize price tracking
        self._buy_prices = deque(maxlen=2880)  # Store buy prices for average calculation
        self._sell_prices = deque(maxlen=2880)  # Store sell prices for average calculation
        self._last_avg_update = 0  # Timestamp of last average update

        # PRODUCTION SAFETY: Initialize buy-low/sell-high enforcement system
        # MODIFIED for own-token market making when DEX coordination is enabled
        if enable_dex_coordination:
            self._enforce_buy_low_sell_high = False
            self.logger().info("SAFETY: Using own-token market making safety mode (DEX coordination enabled)")
            self.logger().info("SAFETY: Enabling daily stop loss and 8-hour average price monitoring")
        else:
            self._enforce_buy_low_sell_high = enforce_buy_low_sell_high
        self._min_profit_margin = min_profit_margin
        self._price_trend_threshold = price_trend_threshold
        self._price_memory_window = price_memory_window

        # ANTI-DRAIN PROTECTION: For own-token market making
        self._enable_anti_drain_protection = False
        self._max_order_imbalance_ratio = Decimal('3.0')  # Max 3:1 buy:sell ratio
        self._max_volume_per_minute = Decimal('10000')  # Max volume per minute
        self._suspicious_volume_threshold = Decimal('5000')  # Volume that triggers scrutiny
        self._rapid_trade_window = 30  # seconds
        self._max_trades_per_window = 10  # Max trades in rapid window
        self._volume_tracking_window = 60  # Track volume over 1 minute
        self._recent_trades = deque(maxlen=100)  # Track recent trades
        self._volume_history = deque(maxlen=60)  # Track volume per second
        self._last_volume_reset = time.time()

        # SOPHISTICATED ATTACK DETECTION
        self._attack_detected = False
        self._attack_start_time = 0.0
        self._attack_start_price = Decimal('0')  # Price when attack started
        self._attack_phase = "NONE"  # NONE, PUMP, DUMP, COOLDOWN
        self._pump_peak_price = Decimal('0')  # Highest price during pump
        self._attack_cooldown = 300.0  # 5 minutes cooldown after attack
        self._price_manipulation_threshold = Decimal('0.02')  # 2% price movement triggers analysis
        self._pump_threshold = Decimal('0.03')  # 3% pump to trigger pump phase
        self._dump_threshold = Decimal('0.03')  # 3% dump to trigger dump phase
        self._liquidity_scaling_factor = Decimal('2.0')  # Scale up liquidity during pump
        self._max_buy_distance_from_attack_start = Decimal('0.01')  # Only buy within 1% of attack start price

        # Initialize safety system state
        self._recent_price_history = deque(maxlen=int(price_memory_window / 10))  # 10-second intervals
        self._current_price_trend = "NEUTRAL"
        self._trend_confidence = 0.0
        self._safe_buy_ceiling = Decimal('0')
        self._safe_sell_floor = Decimal('0')
        self._last_boundary_update = 0.0
        self._boundary_update_interval = 30.0
        self._last_trend_analysis = 0.0
        self._rejected_buy_orders = 0
        self._rejected_sell_orders = 0
        self._last_rejection_log = 0.0
        self._rejection_log_interval = 300.0

        self._all_markets_ready = False
        self._filled_buys_balance = 0
        self._filled_sells_balance = 0
        self._logging_options = logging_options
        self._last_timestamp = 0
        self._status_report_interval = status_report_interval
        self._last_own_trade_price = Decimal('nan')
        self._should_wait_order_cancel_confirmation = should_wait_order_cancel_confirmation
        self._moving_price_band = moving_price_band

        # DEX COORDINATION: Initialize cross-platform coordination
        self._enable_dex_coordination = enable_dex_coordination
        self._dex_chain = dex_chain
        self._dex_network = dex_network
        self._dex_connector = dex_connector
        self._dex_base_token_address = dex_base_token_address
        self._dex_quote_token_address = dex_quote_token_address
        self._dex_pool_fee_tier = dex_pool_fee_tier
        self._dex_pool_address = dex_pool_address
        self._dex_price_update_interval = dex_price_update_interval
        self._dex_price_staleness_threshold = dex_price_staleness_threshold
        self._dex_subgraph_bearer_token = dex_subgraph_bearer_token
        self._dex_subgraph_rate_limit_seconds = dex_subgraph_rate_limit_seconds
        self._enable_arbitrage_protection = enable_arbitrage_protection
        self._max_price_deviation_pct = max_price_deviation_pct / 100  # Convert to decimal
        self._arbitrage_protection_spread_adjustment = arbitrage_protection_spread_adjustment / 100
        self._arbitrage_defense_mode = arbitrage_defense_mode
        self._enable_dex_liquidity_adjustment = enable_dex_liquidity_adjustment
        self._min_dex_liquidity_threshold = min_dex_liquidity_threshold
        self._low_liquidity_spread_multiplier = low_liquidity_spread_multiplier
        self._enable_order_coordination = enable_order_coordination
        self._coordination_delay_seconds = coordination_delay_seconds
        self._max_coordination_attempts = max_coordination_attempts
        self._enable_cross_platform_risk_limits = enable_cross_platform_risk_limits
        self._max_daily_arbitrage_exposure = max_daily_arbitrage_exposure
        self._emergency_stop_price_deviation = emergency_stop_price_deviation / 100

        # Initialize DEX components if enabled
        self._dex_price_delegate = None
        self._cross_platform_coordinator = None
        if self._enable_dex_coordination and self._dex_base_token_address:
            self._initialize_dex_coordination()

        # ±2% DEPTH OPTIMIZATION: Initialize depth optimization parameters
        self._enable_depth_optimization = enable_depth_optimization
        self._depth_optimization_range_pct = depth_optimization_range_pct / 100  # Convert to decimal
        self._depth_budget_allocation_pct = depth_budget_allocation_pct / 100
        self._min_depth_order_size = min_depth_order_size
        self._enable_dynamic_depth_adjustment = enable_dynamic_depth_adjustment
        self._volatility_depth_multiplier = volatility_depth_multiplier
        self._target_cumulative_depth_usd = target_cumulative_depth_usd

        # Initialize depth tracking variables
        self._current_depth_within_range = Decimal("0")
        self._last_depth_calculation = 0.0
        self._depth_calculation_interval = 30.0  # Update depth metrics every 30 seconds
        self._depth_optimization_active = False

        # OWN-TOKEN SAFETY: Enhanced safety for own-token market making
        self._enable_own_token_safety = own_token_market_making or enable_dex_coordination  # Enable when doing own-token MM
        self._daily_loss_limit_usd = Decimal("1000.0")  # Maximum daily loss in USD
        self._order_loss_limit_pct = Decimal("0.05")  # Maximum 5% loss per order
        self._daily_pnl_usd = Decimal("0")  # Track daily P&L
        self._daily_reset_time = 0.0  # Time of last daily reset
        self._eight_hour_buy_total = Decimal("0")  # Total buy volume in 8 hours
        self._eight_hour_sell_total = Decimal("0")  # Total sell volume in 8 hours
        self._eight_hour_buy_value = Decimal("0")  # Total buy value in 8 hours
        self._eight_hour_sell_value = Decimal("0")  # Total sell value in 8 hours
        self._eight_hour_reset_time = 0.0  # Time of last 8-hour reset
        self._last_safety_check = 0.0  # Time of last safety check
        self._safety_violations = 0  # Count of safety violations
        self._max_safety_violations = 3  # Max violations before emergency stop
        self._emergency_stop_active = False  # Emergency stop flag

        self.c_add_markets([market_info.market])

    def _initialize_dex_coordination(self):
        """Initialize DEX price monitoring and cross-platform coordination"""
        try:
            from hummingbot.strategy.dex_asset_price_delegate import DexAssetPriceDelegate
            from hummingbot.strategy.cross_platform_coordinator import CrossPlatformCoordinator

            self.logger().info("DEX_COORDINATION: Starting initialization...")
            self.logger().info(f"DEX_COORDINATION: Before init - _dex_price_delegate exists: {hasattr(self, '_dex_price_delegate')}")
            self.logger().info(f"DEX_COORDINATION: Before init - _cross_platform_coordinator exists: {hasattr(self, '_cross_platform_coordinator')}")

            # Initialize DEX price delegate
            self._dex_price_delegate = DexAssetPriceDelegate(
                market=self._market_info.market,
                base_token_address=self._dex_base_token_address,
                quote_token_address=self._dex_quote_token_address,
                pool_fee_tier=self._dex_pool_fee_tier,
                update_interval=self._dex_price_update_interval,
                staleness_threshold=self._dex_price_staleness_threshold,
                pool_address=self._dex_pool_address,
                bearer_token=self._dex_subgraph_bearer_token,
                rate_limit_seconds=self._dex_subgraph_rate_limit_seconds
            )

            self.logger().info(f"DEX_COORDINATION: After DEX delegate init - _dex_price_delegate exists: {hasattr(self, '_dex_price_delegate')}")
            self.logger().info(f"DEX_COORDINATION: DEX delegate object: {self._dex_price_delegate}")
            self.logger().info(f"DEX_COORDINATION: DEX delegate getattr: {getattr(self, '_dex_price_delegate', 'NOT_FOUND')}")
            self.logger().info(f"DEX_COORDINATION: DEX delegate is None: {self._dex_price_delegate is None}")

            # Initialize cross-platform coordinator
            self._cross_platform_coordinator = CrossPlatformCoordinator(
                mexc_market=self._market_info.market,
                dex_price_delegate=self._dex_price_delegate,
                max_price_deviation_pct=self._max_price_deviation_pct * 100,  # Convert back to percentage
                arbitrage_protection_spread=self._arbitrage_protection_spread_adjustment * 100,
                min_dex_liquidity_threshold=self._min_dex_liquidity_threshold,
                low_liquidity_spread_multiplier=self._low_liquidity_spread_multiplier,
                emergency_stop_deviation=self._emergency_stop_price_deviation * 100
            )

            self.logger().info(f"DEX_COORDINATION: After coordinator init - _cross_platform_coordinator exists: {hasattr(self, '_cross_platform_coordinator')}")
            self.logger().info(f"DEX_COORDINATION: Coordinator object: {self._cross_platform_coordinator}")
            self.logger().info(f"DEX_COORDINATION: Coordinator getattr: {getattr(self, '_cross_platform_coordinator', 'NOT_FOUND')}")
            self.logger().info(f"DEX_COORDINATION: Coordinator is None: {self._cross_platform_coordinator is None}")

            self.logger().info("DEX_COORDINATION: Successfully initialized cross-platform coordination")
            self.logger().info(f"DEX_COORDINATION: Monitoring {self._dex_base_token_address} on {self._dex_chain}")

        except Exception as e:
            import traceback
            self.logger().error(f"DEX_COORDINATION: Failed to initialize: {str(e)}")
            self.logger().error(f"DEX_COORDINATION: Traceback: {traceback.format_exc()}")
            self._enable_dex_coordination = False

    def __init__(self):
        super().__init__()
        self._order_tracker = {}
        self._hanging_orders_tracker = None  # Will be initialized in init_params
        self._last_timestamp = 0.0
        self._last_order_creation_timestamp = 0.0
        self._price_band = None
        self._price_band_refresh_time = 0.0
        self._price_band_refresh_timestamp = 0.0
        self._price_band_refresh_count = 0
        self._should_create_orders = False
        self._moving_price_band = None  # Will be initialized in init_params

        # Improved order refresh mechanism
        self._orders_pending_cancellation = []
        self._new_orders_created_successfully = False

        # Startup and balance tracking
        self._startup_complete = False
        self._startup_timestamp = 0.0
        self._balance_retry_count = 0
        self._max_balance_retries = 3
        self._balance_retry_delay = 2.0  # seconds between balance retries

        # Spread tracking
        self._original_bid_spread = Decimal('0')
        self._original_ask_spread = Decimal('0')
        self._min_allowed_spread = Decimal('0.05')  # 5% minimum spread

        # Status reporting
        self._status_report_interval = 5.0  # Default to 5 seconds
        self._ping_pong_warning_lines = []  # For ping-pong order warnings

        # Logging configuration
        self._price_ratio_log_level = "WARNING"  # Default log level for price ratio warnings

        # Bot attack detection
        self._price_history = deque(maxlen=100)  # Store last 100 price points
        self._last_attack_check = 0.0
        self._in_attack_mode = False
        self._attack_detection_threshold = 0.02  # 2% price movement in 10 seconds
        self._attack_detection_window = 10.0  # seconds
        self._attack_start_price = Decimal('0')

        # Order tracking
        self._order_history = deque(maxlen=100)  # Store recent order events
        self._last_order_check = 0.0

        # Ratio tracking
        self._last_ratio_log = 0.0
        self._ratio_warning_interval = 300.0  # 5 minutes between ratio warnings

        # Price tracking
        self._buy_prices = deque(maxlen=2880)  # Store buy prices for average calculation
        self._sell_prices = deque(maxlen=2880)  # Store sell prices for average calculation
        self._last_avg_update = 0.0  # Timestamp of last average update

        # PRODUCTION SAFETY: Initialize safety system attributes
        self._enforce_buy_low_sell_high = False  # DISABLED for own-token market making
        self._min_profit_margin = Decimal('0.002')  # Default 0.2%
        self._price_trend_threshold = Decimal('0.01')  # Default 1%
        self._price_memory_window = 3600.0  # Default 1 hour
        self._recent_price_history = deque(maxlen=360)  # 10-second intervals for 1 hour
        self._current_price_trend = "NEUTRAL"
        self._trend_confidence = 0.0
        self._safe_buy_ceiling = Decimal('0')
        self._safe_sell_floor = Decimal('0')
        self._last_boundary_update = 0.0
        self._boundary_update_interval = 30.0
        self._last_trend_analysis = 0.0
        self._rejected_buy_orders = 0
        self._rejected_sell_orders = 0
        self._last_rejection_log = 0.0
        self._rejection_log_interval = 300.0

        # ANTI-DRAIN PROTECTION: Initialize anti-drain attributes
        self._enable_anti_drain_protection = False
        self._max_order_imbalance_ratio = Decimal('3.0')
        self._max_volume_per_minute = Decimal('10000')
        self._suspicious_volume_threshold = Decimal('5000')
        self._rapid_trade_window = 30
        self._max_trades_per_window = 10
        self._volume_tracking_window = 60
        self._recent_trades = deque(maxlen=100)
        self._volume_history = deque(maxlen=60)
        self._last_volume_reset = 0.0  # Will be set to time.time() in init_params
        self._attack_detected = False
        self._attack_start_time = 0.0
        self._attack_start_price = Decimal('0')
        self._attack_phase = "NONE"
        self._pump_peak_price = Decimal('0')
        self._attack_cooldown = 300.0
        self._price_manipulation_threshold = Decimal('0.02')
        self._pump_threshold = Decimal('0.03')
        self._dump_threshold = Decimal('0.03')
        self._liquidity_scaling_factor = Decimal('2.0')
        self._max_buy_distance_from_attack_start = Decimal('0.01')

        # ENHANCED LIQUIDITY MONITORING: Initialize with proper time
        self._usd_volume_tracking = deque(maxlen=300)
        self._large_order_threshold = Decimal('100')
        self._balance_drain_threshold = Decimal('0.10')
        self._initial_base_balance = Decimal('0')
        self._initial_quote_balance = Decimal('0')
        self._alert_level = "GREEN"
        self._max_balance_drain_pct = Decimal('0.20')
        self._large_order_history = deque(maxlen=50)
        self._last_balance_check = time.time()
        self._cumulative_buy_volume_usd = Decimal('0')
        self._cumulative_sell_volume_usd = Decimal('0')

        # ADVERSE SELECTION PROTECTION: Initialize with proper time
        self._volatility_1m = Decimal('0.01')
        self._volatility_5m = Decimal('0.01')
        self._volatility_15m = Decimal('0.01')
        self._volatility_regime = "LOW"
        self._price_changes_1m = deque(maxlen=60)
        self._price_changes_5m = deque(maxlen=300)
        self._price_changes_15m = deque(maxlen=900)
        self._last_volatility_update = time.time()
        self._base_spread_multiplier = Decimal('1.0')
        self._min_spread_multiplier = Decimal('0.5')
        self._max_spread_multiplier = Decimal('5.0')

        # ADVERSE SELECTION PROTECTION: Momentum detection
        self._momentum_indicator = Decimal('0')
        self._price_velocity = Decimal('0')
        self._volume_momentum = Decimal('0')
        self._directional_pressure = Decimal('0')
        self._recent_fills = deque(maxlen=100)
        self._aggressive_fill_ratio = Decimal('0')
        self._last_momentum_update = time.time()
        self._momentum_threshold = Decimal('0.3')
        self._adverse_momentum_detected = False
        self._last_price_for_momentum = Decimal('0')
        self._momentum_detection_window = 30.0

        # DEX COORDINATION: Initialize default values to prevent AttributeError
        self._enable_dex_coordination = False  # Default to False, will be set in init_params
        self._dex_chain = "base"
        self._dex_network = "mainnet"
        self._dex_connector = "uniswap"
        self._dex_base_token_address = None
        self._dex_quote_token_address = "0x4200000000000000000000000000000000000006"
        self._dex_pool_fee_tier = 3000
        self._dex_pool_address = None
        self._dex_price_update_interval = 30.0  # Increased for rate limiting
        self._dex_price_staleness_threshold = 90.0  # Increased for rate limiting
        self._dex_subgraph_bearer_token = None
        self._dex_subgraph_rate_limit_seconds = 30
        self._enable_arbitrage_protection = True
        self._max_price_deviation_pct = Decimal("0.02")
        self._arbitrage_protection_spread_adjustment = Decimal("0.005")
        self._arbitrage_defense_mode = "normal"  # Default value, will be overridden in init_params
        self._enable_dex_liquidity_adjustment = True
        self._min_dex_liquidity_threshold = Decimal("10000.0")
        self._low_liquidity_spread_multiplier = Decimal("1.5")
        self._enable_order_coordination = False
        self._coordination_delay_seconds = 2.0
        self._max_coordination_attempts = 3
        self._enable_cross_platform_risk_limits = True
        self._max_daily_arbitrage_exposure = Decimal("5000.0")
        self._emergency_stop_price_deviation = Decimal("0.05")
        self._dex_price_delegate = None
        self._cross_platform_coordinator = None

        # ORDER BOOK INTELLIGENCE: Initialize with proper time
        self._order_book_levels = deque(maxlen=100)
        self._support_resistance_levels = []
        self._bid_ask_spread_history = deque(maxlen=300)
        self._order_book_imbalance = Decimal('0')
        self._last_orderbook_analysis = time.time()
        self._optimal_bid_level = Decimal('0')
        self._optimal_ask_level = Decimal('0')
        self._book_depth_threshold = Decimal('1000')
        self._spread_percentile_90 = Decimal('0.01')
        self._spread_percentile_10 = Decimal('0.001')

        # MARKET IMPACT MODELING: Initialize with proper time
        self._our_market_share = Decimal('0.05')
        self._estimated_daily_volume = Decimal('100000')
        self._our_daily_volume = Decimal('0')
        self._price_impact_model = {}
        self._optimal_order_size = Decimal('0')  # Will be set in init_params
        self._max_single_order_impact = Decimal('0.002')
        self._volume_participation_rate = Decimal('0.1')
        self._last_impact_calculation = time.time()
        self._historical_impacts = deque(maxlen=100)
        self._size_impact_curve = {}







        # Thresholds
        self._upper_threshold = Decimal('0.008')
        self._lower_threshold = Decimal('0.006')

        # Startup delay (seconds) - wait for balances to load
        self._startup_delay = 5.0
        self._sell_wall_spread = Decimal('0.0005')  # 0.05% below upper threshold

        # Order level amounts
        self._bid_order_level_amounts = []
        self._ask_order_level_amounts = []

        # Price ratio configuration
        self._enable_spread_adjustment = False  # Default to False for backward compatibility
        self._spread_adjustment_increment = Decimal('0.01')  # Default 1% increment
        self._max_buy_sell_ratio = Decimal('1.0')  # Default ratio of 1.0 (no adjustment)
        self._max_price_ratio = Decimal('1.0')
        self._spread_adjustment_factor = Decimal('1.0')
        self._last_ratio_warning = 0.0

        # Order tracking
        self._sb_order_tracker = None
        self._limit_order_type = None
        self._all_markets_ready = False
        # Note: _sb_markets is already initialized as set() in parent class StrategyBase
        self._market_info_to_active_orders = {}

        # Average price ratio configuration
        self._max_price_ratio = Decimal('0.99')  # 99% - if buy/sell ratio exceeds this, take action
        self._spread_adjustment_factor = Decimal('1.2')  # 20% spread increase when ratio is high
        self._last_ratio_warning = 0  # Timestamp of last ratio warning
        self._ratio_warning_interval = 300  # 5 minutes between ratio warnings

        # Order tracking cleanup timing (for MEXC exchange)
        self._last_order_sync_time = 0.0



    def all_markets_ready(self):
        # Defensive check: ensure _sb_markets is not None
        if self._sb_markets is None:
            self.logger().error("CRITICAL: _sb_markets is None in all_markets_ready! Reinitializing as empty set.")
            self._sb_markets = set()
        return all([market.ready for market in self._sb_markets])

    @property
    def market_info(self) -> MarketTradingPairTuple:
        return self._market_info

    @property
    def max_order_age(self) -> float:
        return self._max_order_age

    @property
    def minimum_spread(self) -> Decimal:
        return self._minimum_spread

    @property
    def ping_pong_enabled(self) -> bool:
        return self._ping_pong_enabled

    @property
    def ask_order_optimization_depth(self) -> Decimal:
        return self._ask_order_optimization_depth

    @property
    def bid_order_optimization_depth(self) -> Decimal:
        return self._bid_order_optimization_depth

    @property
    def price_type(self) -> PriceType:
        return self._price_type

    @property
    def order_refresh_tolerance_pct(self) -> Decimal:
        return self._order_refresh_tolerance_pct

    @order_refresh_tolerance_pct.setter
    def order_refresh_tolerance_pct(self, value: Decimal):
        self._order_refresh_tolerance_pct = value

    @property
    def order_amount(self) -> Decimal:
        return self._order_amount

    @order_amount.setter
    def order_amount(self, value: Decimal):
        self._order_amount = value

    @property
    def order_levels(self) -> int:
        return self._order_levels

    @order_levels.setter
    def order_levels(self, value: int):
        self._order_levels = value
        self._buy_levels = value
        self._sell_levels = value

    @property
    def buy_levels(self) -> int:
        return self._buy_levels

    @buy_levels.setter
    def buy_levels(self, value: int):
        self._buy_levels = value

    @property
    def sell_levels(self) -> int:
        return self._sell_levels

    @sell_levels.setter
    def sell_levels(self, value: int):
        self._sell_levels = value

    @property
    def order_level_amount(self) -> Decimal:
        return self._order_level_amount

    @order_level_amount.setter
    def order_level_amount(self, value: Decimal):
        self._order_level_amount = value

    @property
    def order_level_spread(self) -> Decimal:
        return self._order_level_spread

    @order_level_spread.setter
    def order_level_spread(self, value: Decimal):
        self._order_level_spread = value

    @property
    def inventory_skew_enabled(self) -> bool:
        return self._inventory_skew_enabled

    @inventory_skew_enabled.setter
    def inventory_skew_enabled(self, value: bool):
        self._inventory_skew_enabled = value

    @property
    def inventory_target_base_pct(self) -> Decimal:
        return self._inventory_target_base_pct

    @inventory_target_base_pct.setter
    def inventory_target_base_pct(self, value: Decimal):
        self._inventory_target_base_pct = value

    @property
    def inventory_range_multiplier(self) -> Decimal:
        return self._inventory_range_multiplier

    @inventory_range_multiplier.setter
    def inventory_range_multiplier(self, value: Decimal):
        self._inventory_range_multiplier = value

    @property
    def hanging_orders_enabled(self) -> bool:
        return self._hanging_orders_enabled

    @hanging_orders_enabled.setter
    def hanging_orders_enabled(self, value: bool):
        self._hanging_orders_enabled = value

    @property
    def hanging_orders_cancel_pct(self) -> Decimal:
        return self._hanging_orders_tracker._hanging_orders_cancel_pct

    @hanging_orders_cancel_pct.setter
    def hanging_orders_cancel_pct(self, value: Decimal):
        self._hanging_orders_tracker._hanging_orders_cancel_pct = value

    @property
    def bid_spread(self) -> Decimal:
        return self._bid_spread

    @bid_spread.setter
    def bid_spread(self, value: Decimal):
        self._bid_spread = value

    @property
    def ask_spread(self) -> Decimal:
        return self._ask_spread

    @ask_spread.setter
    def ask_spread(self, value: Decimal):
        self._ask_spread = value

    @property
    def order_optimization_enabled(self) -> bool:
        return self._order_optimization_enabled

    @order_optimization_enabled.setter
    def order_optimization_enabled(self, value: bool):
        self._order_optimization_enabled = value

    @property
    def order_refresh_time_min(self) -> float:
        return self._order_refresh_time_min

    @order_refresh_time_min.setter
    def order_refresh_time_min(self, value: float):
        self._order_refresh_time_min = value

    @property
    def order_refresh_time_max(self) -> float:
        return self._order_refresh_time_max

    @order_refresh_time_max.setter
    def order_refresh_time_max(self, value: float):
        self._order_refresh_time_max = value

    @property
    def filled_order_delay(self) -> float:
        return self._filled_order_delay

    @filled_order_delay.setter
    def filled_order_delay(self, value: float):
        self._filled_order_delay = value

    @property
    def add_transaction_costs_to_orders(self) -> bool:
        return self._add_transaction_costs_to_orders

    @add_transaction_costs_to_orders.setter
    def add_transaction_costs_to_orders(self, value: bool):
        self._add_transaction_costs_to_orders = value

    @property
    def price_ceiling(self) -> Decimal:
        return self._price_ceiling

    @price_ceiling.setter
    def price_ceiling(self, value: Decimal):
        self._price_ceiling = value

    @property
    def price_floor(self) -> Decimal:
        return self._price_floor

    @price_floor.setter
    def price_floor(self, value: Decimal):
        self._price_floor = value

    @property
    def base_asset(self):
        return self._market_info.base_asset

    @property
    def quote_asset(self):
        return self._market_info.quote_asset

    @property
    def trading_pair(self):
        return self._market_info.trading_pair

    @property
    def order_override(self):
        return self._order_override

    @property
    def split_order_levels_enabled(self):
        return self._split_order_levels_enabled

    @property
    def bid_order_level_spreads(self):
        return self._bid_order_level_spreads

    @property
    def ask_order_level_spreads(self):
        return self._ask_order_level_spreads

    @property
    def bid_order_level_amounts(self):
        return self._bid_order_level_amounts

    @property
    def ask_order_level_amounts(self):
        return self._ask_order_level_amounts

    @order_override.setter
    def order_override(self, value: Dict[str, List[str]]):
        self._order_override = value

    @property
    def moving_price_band_enabled(self) -> bool:
        return self._moving_price_band.enabled

    @moving_price_band_enabled.setter
    def moving_price_band_enabled(self, value: bool):
        self._moving_price_band.switch(value)

    @property
    def price_ceiling_pct(self) -> Decimal:
        return self._moving_price_band.price_ceiling_pct

    @price_ceiling_pct.setter
    def price_ceiling_pct(self, value: Decimal):
        self._moving_price_band.price_ceiling_pct = value
        self._moving_price_band.update(self._current_timestamp, self.get_price())

    @property
    def price_floor_pct(self) -> Decimal:
        return self._moving_price_band.price_floor_pct

    @price_floor_pct.setter
    def price_floor_pct(self, value: Decimal):
        self._moving_price_band.price_floor_pct = value
        self._moving_price_band.update(self._current_timestamp, self.get_price())

    @property
    def price_band_refresh_time(self) -> float:
        return self._moving_price_band.price_band_refresh_time

    @price_band_refresh_time.setter
    def price_band_refresh_time(self, value: Decimal):
        self._moving_price_band.price_band_refresh_time = value
        self._moving_price_band.update(self._current_timestamp, self.get_price())

    @property
    def moving_price_band(self) -> MovingPriceBand:
        return self._moving_price_band

    def get_price(self) -> Decimal:
        price_provider = self._asset_price_delegate or self._market_info
        if self._price_type is PriceType.LastOwnTrade:
            price = self._last_own_trade_price
        elif self._price_type is PriceType.InventoryCost:
            price = price_provider.get_price_by_type(PriceType.MidPrice)
        else:
            price = price_provider.get_price_by_type(self._price_type)

        if price.is_nan():
            price = price_provider.get_price_by_type(PriceType.MidPrice)

        # OWN-TOKEN MARKET MAKING: Use higher of MEXC vs DEX price to minimize arbitrage gap
        if (hasattr(self, '_enable_dex_coordination') and self._enable_dex_coordination and
            hasattr(self, '_dex_price_delegate') and self._dex_price_delegate and
            self._dex_price_delegate.ready):

            try:
                mexc_price = price  # Current MEXC price
                dex_price = self._dex_price_delegate.c_get_mid_price()

                if not dex_price.is_nan() and dex_price > 0:
                    price_gap_pct = abs(mexc_price - dex_price) / min(mexc_price, dex_price) * 100

                    # ENHANCED STRATEGY: Consider multiple factors for optimal pricing
                    if price_gap_pct > 0.0:  # TESTING: Trigger on any price gap (>0.0%)
                        # Factor 1: Use higher price to minimize arbitrage
                        higher_price = max(mexc_price, dex_price)

                        # Factor 2: Check DEX liquidity to avoid pushing into thin liquidity
                        dex_liquidity = self._dex_price_delegate.liquidity_usd
                        if dex_liquidity < Decimal("50000"):  # Low DEX liquidity
                            # Use weighted average favoring MEXC to avoid pushing price too aggressively
                            weight_mexc = Decimal("0.7")  # 70% MEXC, 30% DEX
                            weight_dex = Decimal("0.3")
                            adjusted_price = mexc_price * weight_mexc + dex_price * weight_dex
                            self.logger().info(
                                f"OWN-TOKEN MM: Low DEX liquidity (${dex_liquidity:.0f}), using weighted price "
                                f"${adjusted_price:.8f} (70% MEXC ${mexc_price:.8f}, 30% DEX ${dex_price:.8f})"
                            )
                        else:
                            # Factor 3: High DEX liquidity - use higher price more aggressively
                            if dex_price > mexc_price:
                                # DEX is higher - use 90% of DEX price to bridge gap gradually
                                gap_bridge_factor = Decimal("0.9")
                                adjusted_price = mexc_price + (dex_price - mexc_price) * gap_bridge_factor
                                self.logger().info(
                                    f"OWN-TOKEN MM: Bridging price gap - using ${adjusted_price:.8f} "
                                    f"(90% toward DEX ${dex_price:.8f} from MEXC ${mexc_price:.8f}, gap: {price_gap_pct:.2f}%)"
                                )
                            else:
                                # MEXC is higher - use MEXC price to maintain support
                                adjusted_price = mexc_price
                                self.logger().info(
                                    f"OWN-TOKEN MM: MEXC higher, maintaining MEXC price ${mexc_price:.8f} "
                                    f"vs DEX ${dex_price:.8f} (gap: {price_gap_pct:.2f}%)"
                                )

                        price = adjusted_price
                    else:
                        # Small gap - use simple higher price
                        price = max(mexc_price, dex_price)

            except Exception as e:
                self.logger().warning(f"OWN-TOKEN MM: Error getting DEX price for comparison: {str(e)}")
                # Fall back to MEXC price

        return price

    def get_mid_price(self) -> Decimal:
        return self.c_get_mid_price()

    cdef object c_get_mid_price(self):
        cdef:
            AssetPriceDelegate delegate = self._asset_price_delegate
            object mid_price
        if self._asset_price_delegate is not None:
            mid_price = delegate.c_get_mid_price()
        else:
            mid_price = self._market_info.get_mid_price()
        return mid_price

    @property
    def hanging_order_ids(self) -> List[str]:
        return [o.order_id for o in self._hanging_orders_tracker.strategy_current_hanging_orders]

    @property
    def market_info_to_active_orders(self) -> Dict[MarketTradingPairTuple, List[LimitOrder]]:
        return self._sb_order_tracker.market_pair_to_active_orders

    @property
    def active_orders(self) -> List[LimitOrder]:
        if self._market_info not in self.market_info_to_active_orders:
            return []
        return self.market_info_to_active_orders[self._market_info]

    @property
    def active_buys(self) -> List[LimitOrder]:
        return [o for o in self.active_orders if o.is_buy]

    @property
    def active_sells(self) -> List[LimitOrder]:
        return [o for o in self.active_orders if not o.is_buy]

    @property
    def active_non_hanging_orders(self) -> List[LimitOrder]:
        orders = [o for o in self.active_orders if not self._hanging_orders_tracker.is_order_id_in_hanging_orders(o.client_order_id)]
        return orders

    @property
    def logging_options(self) -> int:
        return self._logging_options

    @logging_options.setter
    def logging_options(self, int64_t logging_options):
        self._logging_options = logging_options

    @property
    def hanging_orders_tracker(self):
        return self._hanging_orders_tracker

    @property
    def asset_price_delegate(self) -> AssetPriceDelegate:
        return self._asset_price_delegate

    @asset_price_delegate.setter
    def asset_price_delegate(self, value):
        self._asset_price_delegate = value

    @property
    def inventory_cost_price_delegate(self) -> AssetPriceDelegate:
        return self._inventory_cost_price_delegate

    @inventory_cost_price_delegate.setter
    def inventory_cost_price_delegate(self, value):
        self._inventory_cost_price_delegate = value

    def inventory_skew_stats_data_frame(self) -> Optional[pd.DataFrame]:
        cdef:
            ExchangeBase market = self._market_info.market

        price = self.get_price()
        base_asset_amount, quote_asset_amount = self.c_get_adjusted_available_balance(self.active_orders)
        total_order_size = calculate_total_order_size(self._order_amount, self._order_level_amount, self._order_levels)

        base_asset_value = base_asset_amount * price
        quote_asset_value = quote_asset_amount / price if price > s_decimal_zero else s_decimal_zero
        total_value = base_asset_amount + quote_asset_value
        total_value_in_quote = (base_asset_amount * price) + quote_asset_amount

        base_asset_ratio = (base_asset_amount / total_value
                            if total_value > s_decimal_zero
                            else s_decimal_zero)
        quote_asset_ratio = Decimal("1") - base_asset_ratio if total_value > 0 else 0
        target_base_ratio = self._inventory_target_base_pct
        inventory_range_multiplier = self._inventory_range_multiplier
        target_base_amount = (total_value * target_base_ratio
                              if price > s_decimal_zero
                              else s_decimal_zero)
        target_base_amount_in_quote = target_base_ratio * total_value_in_quote
        target_quote_amount = (1 - target_base_ratio) * total_value_in_quote

        base_asset_range = total_order_size * self._inventory_range_multiplier
        base_asset_range = min(base_asset_range, total_value * Decimal("0.5"))
        high_water_mark = target_base_amount + base_asset_range
        low_water_mark = max(target_base_amount - base_asset_range, s_decimal_zero)
        low_water_mark_ratio = (low_water_mark / total_value
                                if total_value > s_decimal_zero
                                else s_decimal_zero)
        high_water_mark_ratio = (high_water_mark / total_value
                                 if total_value > s_decimal_zero
                                 else s_decimal_zero)
        high_water_mark_ratio = min(1.0, high_water_mark_ratio)
        total_order_size_ratio = (self._order_amount * Decimal("2") / total_value
                                  if total_value > s_decimal_zero
                                  else s_decimal_zero)
        bid_ask_ratios = c_calculate_bid_ask_ratios_from_base_asset_ratio(
            float(base_asset_amount),
            float(quote_asset_amount),
            float(price),
            float(target_base_ratio),
            float(base_asset_range)
        )
        inventory_skew_df = pd.DataFrame(data=[
            [f"Target Value ({self.quote_asset})", f"{target_base_amount_in_quote:.4f}",
             f"{target_quote_amount:.4f}"],
            ["Current %", f"{base_asset_ratio:.1%}", f"{quote_asset_ratio:.1%}"],
            ["Target %", f"{target_base_ratio:.1%}", f"{1 - target_base_ratio:.1%}"],
            ["Inventory Range", f"{low_water_mark_ratio:.1%} - {high_water_mark_ratio:.1%}",
             f"{1 - high_water_mark_ratio:.1%} - {1 - low_water_mark_ratio:.1%}"],
            ["Order Adjust %", f"{bid_ask_ratios.bid_ratio:.1%}", f"{bid_ask_ratios.ask_ratio:.1%}"]
        ])
        return inventory_skew_df

    def pure_mm_assets_df(self, to_show_current_pct: bool) -> pd.DataFrame:
        market, trading_pair, base_asset, quote_asset = self._market_info
        price = self._market_info.get_mid_price()
        base_balance = float(market.get_balance(base_asset))
        quote_balance = float(market.get_balance(quote_asset))
        available_base_balance = float(market.get_available_balance(base_asset))
        available_quote_balance = float(market.get_available_balance(quote_asset))
        base_value = base_balance * float(price)
        total_in_quote = base_value + quote_balance
        base_ratio = base_value / total_in_quote if total_in_quote > 0 else 0
        quote_ratio = quote_balance / total_in_quote if total_in_quote > 0 else 0
        data=[
            ["", base_asset, quote_asset],
            ["Total Balance", round(base_balance, 4), round(quote_balance, 4)],
            ["Available Balance", round(available_base_balance, 4), round(available_quote_balance, 4)],
            [f"Current Value ({quote_asset})", round(base_value, 4), round(quote_balance, 4)]
        ]
        if to_show_current_pct:
            data.append(["Current %", f"{base_ratio:.1%}", f"{quote_ratio:.1%}"])
        df = pd.DataFrame(data=data)
        return df

    def active_orders_df(self) -> pd.DataFrame:
        market, trading_pair, base_asset, quote_asset = self._market_info
        price = self.get_price()
        active_orders = self.active_orders
        no_sells = len([o for o in active_orders if not o.is_buy and o.client_order_id and
                        not self._hanging_orders_tracker.is_order_id_in_hanging_orders(o.client_order_id)])
        active_orders.sort(key=lambda x: x.price, reverse=True)
        columns = ["Level", "Type", "Price", "Spread", "Amount (Orig)", "Amount (Adj)", "Age"]
        data = []
        lvl_buy, lvl_sell = 0, 0
        for idx in range(0, len(active_orders)):
            order = active_orders[idx]
            is_hanging_order = self._hanging_orders_tracker.is_order_id_in_hanging_orders(order.client_order_id)
            amount_orig = ""
            if not is_hanging_order:
                if order.is_buy:
                    level = lvl_buy + 1
                    lvl_buy += 1
                else:
                    level = no_sells - lvl_sell
                    lvl_sell += 1
                amount_orig = self._order_amount + ((level - 1) * self._order_level_amount)
            else:
                level_for_calculation = lvl_buy if order.is_buy else lvl_sell
                amount_orig = self._order_amount + ((level_for_calculation - 1) * self._order_level_amount)
                level = "hang"
            spread = 0 if price == 0 else abs(order.price - price)/price
            age = pd.Timestamp(order_age(order, self._current_timestamp), unit='s').strftime('%H:%M:%S')
            data.append([
                level,
                "buy" if order.is_buy else "sell",
                float(order.price),
                f"{spread:.2%}",
                amount_orig,
                float(order.quantity),
                age
            ])

        return pd.DataFrame(data=data, columns=columns)

    def market_status_data_frame(self, market_trading_pair_tuples: List[MarketTradingPairTuple]) -> pd.DataFrame:
        markets_data = []
        markets_columns = ["Exchange", "Market", "Best Bid", "Best Ask", f"Ref Price ({self._price_type.name})"]
        if self._price_type is PriceType.LastOwnTrade and self._last_own_trade_price.is_nan():
            markets_columns[-1] = "Ref Price (MidPrice)"
        market_books = [(self._market_info.market, self._market_info.trading_pair)]
        if type(self._asset_price_delegate) is OrderBookAssetPriceDelegate:
            market_books.append((self._asset_price_delegate.market, self._asset_price_delegate.trading_pair))
        for market, trading_pair in market_books:
            bid_price = market.get_price(trading_pair, False)
            ask_price = market.get_price(trading_pair, True)
            ref_price = float("nan")
            if market == self._market_info.market and self._inventory_cost_price_delegate is not None:
                # We're using inventory_cost, show it's price
                ref_price = self._inventory_cost_price_delegate.get_price()
                if ref_price is None:
                    ref_price = self.get_price()
            elif market == self._market_info.market and self._asset_price_delegate is None:
                ref_price = self.get_price()
            elif (
                self._asset_price_delegate is not None
                and market == self._asset_price_delegate.market
                and self._price_type is not PriceType.LastOwnTrade
            ):
                ref_price = self._asset_price_delegate.get_price_by_type(self._price_type)
            markets_data.append([
                market.display_name,
                trading_pair,
                float(bid_price),
                float(ask_price),
                float(ref_price)
            ])
        return pd.DataFrame(data=markets_data, columns=markets_columns).replace(np.nan, '', regex=True)

    def format_status(self) -> str:
        if not self._all_markets_ready:
            return "Market connectors are not ready."
        cdef:
            list lines = []
            list warning_lines = []
        warning_lines.extend(self._ping_pong_warning_lines)
        warning_lines.extend(self.network_warning([self._market_info]))

        markets_df = map_df_to_str(self.market_status_data_frame([self._market_info]))
        lines.extend(["", "  Markets:"] + ["    " + line for line in markets_df.to_string(index=False).split("\n")])

        assets_df = map_df_to_str(self.pure_mm_assets_df(not self._inventory_skew_enabled))
        # append inventory skew stats.
        if self._inventory_skew_enabled:
            inventory_skew_df = map_df_to_str(self.inventory_skew_stats_data_frame())
            assets_df = pd.concat(
                [assets_df, inventory_skew_df], join="inner")

        first_col_length = max(*assets_df[0].apply(len))
        df_lines = assets_df.to_string(index=False, header=False,
                                       formatters={0: ("{:<" + str(first_col_length) + "}").format}).split("\n")
        lines.extend(["", "  Assets:"] + ["    " + line for line in df_lines])

        # See if there're any open orders.
        if len(self.active_orders) > 0:
            df = map_df_to_str(self.active_orders_df())
            lines.extend(["", "  Orders:"] + ["    " + line for line in df.to_string(index=False).split("\n")])
        else:
            lines.extend(["", "  No active maker orders."])

        warning_lines.extend(self.balance_warning([self._market_info]))

        if len(warning_lines) > 0:
            lines.extend(["", "*** WARNINGS ***"] + warning_lines)

        return "\n".join(lines)

    # The following exposed Python functions are meant for unit tests
    # ---------------------------------------------------------------
    def execute_orders_proposal(self, proposal: Proposal):
        return self.c_execute_orders_proposal(proposal)

    def cancel_order(self, order_id: str):
        return self.c_cancel_order(self._market_info, order_id)

    # ---------------------------------------------------------------

    cdef c_start(self, Clock clock, double timestamp):
        StrategyBase.c_start(self, clock, timestamp)
        self._last_timestamp = timestamp

        # Set initial create timestamp with random time between min and max refresh times
        self._create_timestamp = self.c_create_timer(
            timestamp,
            self._order_refresh_time_min,
            self._order_refresh_time_max
        )
        # Set flag to allow order creation on first tick
        self._should_create_orders = True
        if self._logging_options & self.OPTION_LOG_STATUS_REPORT:
            self.logger().info(f"Initial order refresh scheduled in {self._create_timestamp - timestamp:.2f} seconds")

        self._hanging_orders_tracker.register_events(self.active_markets)

        if self._hanging_orders_enabled:
            # start tracking any restored limit order
            restored_order_ids = self.c_track_restored_orders(self.market_info)
            # make restored order hanging orders
            for order_id in restored_order_ids:
                order = next(o for o in self.market_info.market.limit_orders if o.client_order_id == order_id)
                if order:
                    self._hanging_orders_tracker.add_as_hanging_order(order)

    cdef c_stop(self, Clock clock):
        self._hanging_orders_tracker.unregister_events(self.active_markets)

        # DEX COORDINATION: Clean up DEX components
        if hasattr(self, '_dex_price_delegate') and self._dex_price_delegate:
            self._dex_price_delegate.stop()
            self.logger().info("DEX_COORDINATION: Stopped DEX price delegate")

        if hasattr(self, '_cross_platform_coordinator') and self._cross_platform_coordinator:
            self._cross_platform_coordinator.stop()
            self.logger().info("DEX_COORDINATION: Stopped cross-platform coordinator")

        StrategyBase.c_stop(self, clock)

    cdef object c_create_timer(self, double timestamp, double time_delta_min, double time_delta_max):
        import random
        # Ensure time_delta is at least 0.1 seconds and within bounds
        time_delta_min = max(0.1, time_delta_min)
        time_delta_max = max(time_delta_min, time_delta_max)  # Ensure max >= min
        random_delta = random.uniform(time_delta_min, time_delta_max)
        # Ensure the timestamp is in the future
        return timestamp + max(0.1, random_delta)

    cdef c_tick(self, double timestamp):
        StrategyBase.c_tick(self, timestamp)

        cdef:
            int64_t current_tick = 0
            int64_t last_tick = 0
            bint should_report_warnings = False
            object proposal
            double current_time = time.time()
            double time_since_start = current_time - self._startup_timestamp

        # Initialize startup timestamp on first tick
        if self._startup_timestamp == 0.0:
            self._startup_timestamp = current_time
            self.logger().info("Initializing strategy startup sequence...")

        # Ensure _status_report_interval is valid to prevent division by zero
        if self._status_report_interval <= 0:
            self._status_report_interval = 5.0  # Default to 5 seconds if not set

        current_tick = <int64_t>(timestamp // self._status_report_interval)
        last_tick = <int64_t>(self._last_timestamp // self._status_report_interval)
        should_report_warnings = ((current_tick > last_tick) and
                                 (self._logging_options & self.OPTION_LOG_STATUS_REPORT))

        try:
            # Check if markets are ready
            if not self._all_markets_ready:
                # Defensive check: ensure _sb_markets is not None
                if self._sb_markets is None:
                    self.logger().error("CRITICAL: _sb_markets is None! Reinitializing as empty set.")
                    self._sb_markets = set()
                self._all_markets_ready = all([market.ready for market in self._sb_markets])
                if self._asset_price_delegate is not None and self._all_markets_ready:
                    self._all_markets_ready = self._asset_price_delegate.ready

                # CRITICAL: Also check if order book tracker is ready
                if self._all_markets_ready:
                    for market in self._sb_markets:
                        if hasattr(market, 'order_book_tracker') and market.order_book_tracker is not None:
                            if not market.order_book_tracker.ready:
                                self._all_markets_ready = False
                                if should_report_warnings:
                                    self.logger().warning(f"Order book tracker for {market.name} is not ready yet.")
                                break
                            # Check if specific trading pair order book exists
                            if self.trading_pair not in market.order_book_tracker.order_books:
                                self._all_markets_ready = False
                                if should_report_warnings:
                                    self.logger().warning(f"Order book for {self.trading_pair} is not initialized yet.")
                                break

                            # CRITICAL: Check if order book has actual bid/ask data
                            try:
                                order_book = market.order_book_tracker.order_books[self.trading_pair]
                                if order_book is None:
                                    self._all_markets_ready = False
                                    if should_report_warnings:
                                        self.logger().warning(f"Order book object for {self.trading_pair} is None.")
                                    break

                                # Check if order book has bid and ask entries
                                if not order_book.bid_entries() or not order_book.ask_entries():
                                    self._all_markets_ready = False
                                    if should_report_warnings:
                                        self.logger().warning(f"Order book for {self.trading_pair} has no bid/ask entries yet.")
                                    break

                                # Additional validation: try to get order book and price to ensure it's functional
                                try:
                                    # First try to get the order book directly
                                    test_order_book = market.get_order_book(self.trading_pair)
                                    if test_order_book is None:
                                        self._all_markets_ready = False
                                        if should_report_warnings:
                                            self.logger().warning(f"Order book object for {self.trading_pair} is None.")
                                        break

                                    # Then try to get mid price
                                    test_price = market.get_mid_price(self.trading_pair)
                                    if test_price is None or test_price.is_nan():
                                        self._all_markets_ready = False
                                        if should_report_warnings:
                                            self.logger().warning(f"Cannot get valid mid price for {self.trading_pair}.")
                                        break

                                    # Check if trading rules are loaded (required for price quantization)
                                    if hasattr(market, '_trading_rules') and self.trading_pair not in market._trading_rules:
                                        self._all_markets_ready = False
                                        if should_report_warnings:
                                            self.logger().warning(f"Trading rules for {self.trading_pair} are not loaded yet.")
                                        break

                                    # Finally, try the same method that will be used in order creation
                                    try:
                                        test_price_by_type = market.get_price_by_type(self.trading_pair, PriceType.MidPrice)
                                        if test_price_by_type is None or test_price_by_type.is_nan():
                                            self._all_markets_ready = False
                                            if should_report_warnings:
                                                self.logger().warning(f"Cannot get valid price by type for {self.trading_pair}.")
                                            break
                                    except (ValueError, KeyError) as ve:
                                        if "No order book exists" in str(ve) or self.trading_pair in str(ve):
                                            self._all_markets_ready = False
                                            if should_report_warnings:
                                                self.logger().warning(f"Order book or trading rules validation failed for {self.trading_pair}: {str(ve)}")
                                            break
                                        else:
                                            raise  # Re-raise if it's a different error

                                except Exception as price_error:
                                    self._all_markets_ready = False
                                    if should_report_warnings:
                                        self.logger().warning(f"Error validating order book functionality for {self.trading_pair}: {str(price_error)}")
                                    break

                            except Exception as ob_error:
                                self._all_markets_ready = False
                                if should_report_warnings:
                                    self.logger().warning(f"Error validating order book for {self.trading_pair}: {str(ob_error)}")
                                break

                if not self._all_markets_ready:
                    if should_report_warnings:
                        self.logger().warning("Markets are not ready. No market making trades are permitted.")
                    return
                else:
                    self.logger().info("Markets are ready. Waiting for startup delay...")

            # Apply startup delay
            if not self._startup_complete and time_since_start < self._startup_delay:
                if should_report_warnings:
                    self.logger().info(f"Startup delay: Waiting {self._startup_delay - time_since_start:.1f} seconds for balances to load...")
                return

            # Mark startup as complete after delay
            if not self._startup_complete:
                self._startup_complete = True
                self.logger().info("Startup sequence complete. Starting market making...")

            if should_report_warnings:
                # Defensive check: ensure _sb_markets is not None
                if self._sb_markets is None:
                    self.logger().error("CRITICAL: _sb_markets is None! Reinitializing as empty set.")
                    self._sb_markets = set()
                if not all([market.network_status is NetworkStatus.CONNECTED for market in self._sb_markets]):
                    self.logger().warning(f"WARNING: Some markets are not connected or are down at the moment. Market "
                                        f"making may be dangerous when markets or networks are unstable.")

                # Log periodic ±2% depth status if depth optimization is enabled
                if hasattr(self, '_enable_depth_optimization') and self._enable_depth_optimization:
                    self._log_periodic_depth_status()

            # Track prices from filled orders for ratio calculation
            for order in self.active_orders:
                if order.is_filled:
                    self._update_avg_prices(order.is_buy, order.price)

            # Check price ratio and adjust strategy if needed
            if self._enable_spread_adjustment:
                try:
                    avg_ratio = self._get_avg_buy_sell_ratio()

                    # Log ratio status periodically
                    if current_time - self._last_ratio_log > 300:  # Log every 5 minutes
                        self.logger().info(
                            f"Price ratio monitoring - "
                            f"Current ratio: {avg_ratio:.4f}, "
                            f"Threshold: {float(self._max_buy_sell_ratio):.4f}, "
                            f"Buy prices: {len(self._buy_prices)}, "
                            f"Sell prices: {len(self._sell_prices)}"
                        )
                        self._last_ratio_log = current_time

                    # Only log ratio warnings if we have enough trades
                    min_required_trades = 15
                    if (avg_ratio > float(self._max_buy_sell_ratio) and
                        len(self._buy_prices) >= min_required_trades and
                        len(self._sell_prices) >= min_required_trades):
                        self.logger().warning(
                            f"Warning: High buy/sell price ratio detected ({avg_ratio:.4f} > {float(self._max_buy_sell_ratio):.4f}). "
                            f"Adjusting strategy to protect profitability."
                        )

                except Exception as e:
                    self.logger().error(f"Error in price ratio monitoring: {str(e)}", exc_info=True)

            # Create and process order proposals
            proposal = None

            # Handle order refresh timing
            current_time = self._current_timestamp

            # If we're not already creating orders and it's time to create new ones
            if not self._should_create_orders and current_time >= self._create_timestamp:
                self.logger().debug(f"[DEBUG] Setting _should_create_orders to True. Current time: {current_time}, Next refresh: {self._create_timestamp}")
                self._should_create_orders = True

                # Calculate next refresh time - ensure it's in the future and within bounds
                next_refresh_in = max(0.1, random.uniform(
                    float(self._order_refresh_time_min),
                    float(self._order_refresh_time_max)
                ))

                # Ensure the next refresh time is in the future
                self._create_timestamp = current_time + next_refresh_in

                if self._logging_options & self.OPTION_LOG_STATUS_REPORT:
                    self.logger().info(f"Next order refresh scheduled in {next_refresh_in:.2f} seconds between {float(self._order_refresh_time_min):.2f} and {float(self._order_refresh_time_max):.2f}")

            elif not self._should_create_orders:
                time_until_refresh = max(0, self._create_timestamp - current_time)
                self.logger().debug(
                    f"[DEBUG] Not creating orders yet. "
                    f"Should create: {self._should_create_orders}, "
                    f"Time until refresh: {time_until_refresh:.2f}s, "
                    f"Next refresh at: {self._create_timestamp}"
                )

                # If we're in a state where refresh time is in the past, force update it
                if time_until_refresh < 0:
                    self.logger().warning(f"Negative refresh time detected: {time_until_refresh:.2f}s. Resetting refresh timer.")
                    self._create_timestamp = current_time + max(0.1, float(self._order_refresh_time_min))

            # After order creation, _should_create_orders will be set to False elsewhere (after order processing)

            if self._should_create_orders:
                try:
                    # Check if we should skip due to rate limiting (MEXC specific)
                    if hasattr(self._market_info.market, 'name') and self._market_info.market.name == "mexc":
                        # Clean up stale order tracking periodically (every 10 minutes)
                        if self._current_timestamp - self._last_order_sync_time > 600:  # 10 minutes
                            if hasattr(self._market_info.market, 'clean_stale_order_tracking_sync'):
                                self.logger().info("🧹 Performing periodic order tracking cleanup...")
                                self._market_info.market.clean_stale_order_tracking_sync(self.trading_pair)
                                self._last_order_sync_time = self._current_timestamp

                        # Use the synchronous rate limit check method
                        if hasattr(self._market_info.market, 'should_skip_due_to_rate_limit_sync'):
                            should_skip = self._market_info.market.should_skip_due_to_rate_limit_sync()
                            if should_skip:
                                # The logging is already done in the sync method, just return
                                return

                    # 1. Create base order proposals
                    proposal = self.c_create_base_proposal()
                    if proposal is None:
                        self.logger().warning("Failed to create base proposal")
                        return

                    # 2. Apply functions that limit numbers of buys and sells proposal
                    self.c_apply_order_levels_modifiers(proposal)
                    # 3. Apply functions that modify orders price
                    self.c_apply_order_price_modifiers(proposal)
                    # 4. Apply functions that modify orders size
                    self.c_apply_order_size_modifiers(proposal)
                    # 4.5. Apply ±2% depth optimization if enabled
                    self.c_apply_depth_optimization(proposal)
                    # 5. Apply budget constraint, i.e. can't buy/sell more than what you have.
                    self.c_apply_budget_constraint(proposal)

                    if not self._take_if_crossed:
                        self.c_filter_out_takers(proposal)

                    # MOVED: Order cancellation logic should only run at scheduled refresh time
                    # First, cancel all active non-hanging orders
                    active_orders = self.active_non_hanging_orders

                    # DEBUG: Log detailed order detection information (only at refresh time)
                    all_active_orders = self.active_orders
                    # FIXED: Don't use market.limit_orders as it contains stale Hummingbot tracking data
                    # Instead, get actual orders from MEXC exchange if available
                    market_orders = []
                    try:
                        if hasattr(self._market_info.market, 'get_open_orders'):
                            # Use async call to get actual orders from MEXC (this will be empty list if it fails)
                            import asyncio
                            try:
                                # Try to get actual orders from exchange, but don't block if it fails
                                loop = asyncio.get_event_loop()
                                if loop.is_running():
                                    # We're in an async context, but can't await here, so skip this debug info
                                    market_orders = []
                                else:
                                    market_orders = loop.run_until_complete(self._market_info.market.get_open_orders(self.trading_pair))
                            except:
                                market_orders = []
                        else:
                            # Fallback to in_flight_orders which is more reliable than limit_orders
                            market_orders = list(self._market_info.market.in_flight_orders.values())
                    except Exception as e:
                        self.logger().debug(f"ORDER_DEBUG: Could not fetch market orders: {e}")
                        market_orders = []

                    self.logger().info(f"ORDER_DEBUG: active_orders property: {len(all_active_orders)} orders")
                    self.logger().info(f"ORDER_DEBUG: active_non_hanging_orders: {len(active_orders)} orders")
                    self.logger().info(f"ORDER_DEBUG: actual exchange orders: {len(market_orders)} orders")

                    if all_active_orders:
                        order_details = [f"{o.client_order_id} ({'BUY' if o.is_buy else 'SELL'} {o.quantity} @ {o.price})" for o in all_active_orders[:10]]  # Limit to first 10
                        if len(all_active_orders) > 10:
                            order_details.append(f"... and {len(all_active_orders) - 10} more")
                        self.logger().info(f"ORDER_DEBUG: All active orders: {', '.join(order_details)}")

                    # Don't log market orders details if there are too many (indicates stale tracking)
                    if market_orders and len(market_orders) < 100:
                        if hasattr(market_orders[0], 'client_order_id'):
                            # These are LimitOrder objects
                            market_order_details = [f"{o.client_order_id} ({'BUY' if o.is_buy else 'SELL'} {o.quantity} @ {o.price})" for o in market_orders[:10]]
                        else:
                            # These are exchange order dictionaries
                            market_order_details = [f"{o.get('orderId', 'N/A')} ({o.get('side', 'N/A')} {o.get('origQty', 'N/A')} @ {o.get('price', 'N/A')})" for o in market_orders[:10]]
                        if len(market_orders) > 10:
                            market_order_details.append(f"... and {len(market_orders) - 10} more")
                        self.logger().info(f"ORDER_DEBUG: Exchange orders: {', '.join(market_order_details)}")
                    elif len(market_orders) >= 100:
                        self.logger().warning(f"ORDER_DEBUG: Too many market orders ({len(market_orders)}) - likely stale tracking data!")

                    # REMOVED: The problematic fallback that used stale market.limit_orders data

                    # IMPROVED ORDER REFRESH: Create new orders first, then cancel old ones
                    # This prevents orderbook gaps during refresh

                    # Step 1: If we have pending cancellations, check if they're complete
                    if self._orders_pending_cancellation:
                        self._check_and_cleanup_pending_cancellations()
                        # If we still have pending cancellations, wait
                        if self._orders_pending_cancellation:
                            self.logger().debug(f"Still waiting for {len(self._orders_pending_cancellation)} order cancellations to complete")
                            self._should_create_orders = False
                            return

                    # Step 2: Create new orders first (while old orders are still active)
                    if proposal is not None and not self._new_orders_created_successfully:
                        # Check if we're using batch orders (MEXC with simplified 3-step approach)
                        is_using_batch_orders = (
                            hasattr(self._market_info.market, 'name') and
                            self._market_info.market.name == "mexc" and
                            hasattr(self._market_info.market, 'batch_order_create')
                        )

                        if is_using_batch_orders:
                            # For batch orders, don't store orders for cancellation
                            # The simplified 3-step approach handles this automatically
                            self.logger().info(f"Using batch orders - order cancellation handled automatically by 3-step approach")
                        else:
                            # Store current active orders for later cancellation (individual orders only)
                            if active_orders and not self._orders_pending_cancellation:
                                self._orders_pending_cancellation = [
                                    order.client_order_id for order in active_orders
                                    if not self._hanging_orders_tracker.is_potential_hanging_order(order)
                                ]
                                self.logger().info(f"Stored {len(self._orders_pending_cancellation)} orders for cancellation after new orders are created")

                        # Create new orders
                        self.logger().info(f"Creating new orders first to avoid orderbook gaps")

                        self.c_cancel_orders_below_min_spread()

                        if self.c_to_create_orders(proposal) and self._startup_complete:
                            # Log ±2% depth BEFORE placing orders
                            self._log_market_depth_before_orders()

                            self.c_execute_orders_proposal(proposal)

                            # Log ±2% depth AFTER placing orders
                            self._log_market_depth_after_orders(proposal)

                            # Log order creation
                            for buy in proposal.buys:
                                self._log_order_event("BUY_ORDER_CREATED", "", self.trading_pair,
                                                   "BUY", str(buy.size), str(buy.price))
                            for sell in proposal.sells:
                                self._log_order_event("SELL_ORDER_CREATED", "", self.trading_pair,
                                                   "SELL", str(sell.size), str(sell.price))

                            self.logger().info(f"Successfully created {len(proposal.buys)} bid and {len(proposal.sells)} ask orders")

                            # Mark that new orders were created successfully
                            self._new_orders_created_successfully = True

                    # Step 3: Cancel old orders after new ones are successfully created
                    if self._new_orders_created_successfully and self._orders_pending_cancellation:
                        # Check if we're using batch orders (MEXC with simplified 3-step approach)
                        is_using_batch_orders = (
                            hasattr(self._market_info.market, 'name') and
                            self._market_info.market.name == "mexc" and
                            hasattr(self._market_info.market, 'batch_order_create')
                        )

                        if is_using_batch_orders:
                            # For batch orders, the simplified 3-step approach handles cancellation
                            # No need to cancel here - it's already handled in the batch order creation
                            self.logger().info(f"Using batch orders - cancellation handled by simplified 3-step approach")
                            # Clear pending cancellations since they're handled by batch approach
                            self._orders_pending_cancellation = []
                        else:
                            # For individual orders, use the old cancellation system
                            self.logger().info(f"New orders created successfully, now canceling {len(self._orders_pending_cancellation)} old orders")
                            self.c_cancel_orders_rate_limited(list(self._orders_pending_cancellation))

                        # Note: We don't clear _orders_pending_cancellation here for individual orders
                        # It will be cleared by _check_and_cleanup_pending_cancellations() when cancellations complete

                    # Step 4: Reset flags when cycle is complete
                    if not self._orders_pending_cancellation and self._new_orders_created_successfully:
                        self.logger().debug("Order refresh cycle completed successfully")
                        self._new_orders_created_successfully = False
                        self._should_create_orders = False
                    elif not self._new_orders_created_successfully:
                        # Reset flag if no new orders were created
                        self._should_create_orders = False

                except Exception as e:
                    self.logger().error(f"Error creating order proposal: {str(e)}", exc_info=True)
                    # Reset flags on error
                    self._should_create_orders = False
                    self._new_orders_created_successfully = False
                    self._orders_pending_cancellation = []
                    return

            # Process hanging orders (this can run every tick)
            try:
                self._hanging_orders_tracker.process_tick()
                self.c_cancel_active_orders_on_max_age_limit()

            except Exception as e:
                self.logger().error(f"Error in hanging order processing: {str(e)}", exc_info=True)

        except Exception as e:
            self.logger().error(f"Unexpected error in c_tick: {str(e)}", exc_info=True)
            raise

        finally:
            self._last_timestamp = timestamp

    def _round_to_significant(self, price: Decimal, increment: Decimal = None) -> Decimal:
        """Round price to significant levels using exchange price quantum"""
        if increment is None:
            # Use the exchange's price quantum for proper rounding
            try:
                market = self._market_info.market
                increment = market.c_get_order_price_quantum(self.trading_pair, price)
            except:
                # Fallback to a much smaller increment if exchange quantum fails
                increment = Decimal('0.00000001')  # 8 decimal places

        return (price / increment).quantize(Decimal('1'), rounding=ROUND_DOWN) * increment

    def _is_bot_attack(self, current_price: Decimal) -> bool:
        """Detect potential bot attack based on rapid price movement"""
        now = time.time()
        if now - self._last_attack_check < 1:  # Check max once per second
            return self._in_attack_mode

        self._last_attack_check = now
        self._price_history.append((now, current_price))

        # Remove old entries
        while self._price_history and now - self._price_history[0][0] > self._attack_detection_window:
            self._price_history.popleft()

        if len(self._price_history) < 2:
            return False

        # Calculate price change percentage
        price_change = abs((current_price - self._price_history[0][1]) / self._price_history[0][1])

        if price_change > self._attack_detection_threshold and not self._in_attack_mode:
            self._in_attack_mode = True
            self._attack_start_price = current_price
            self.logger().warning(f"Potential bot attack detected! Price changed by {price_change*100:.2f}%")
        elif price_change < self._attack_detection_threshold / 2 and self._in_attack_mode:
            self._in_attack_mode = False
            self.logger().info("Bot attack condition cleared")

        return self._in_attack_mode

    def _analyze_price_trend_and_update_boundaries(self, current_price: Decimal) -> None:
        """
        PRODUCTION SAFETY: Analyze price trends and update safe trading boundaries.

        This method is CRITICAL for preventing systematic losses by:
        1. Tracking price trends over the last hour
        2. Setting dynamic buy/sell boundaries based on recent price action
        3. Ensuring we don't buy high during uptrends or sell low during downtrends

        Args:
            current_price: Current market price
        """
        now = time.time()

        # Update price history every 10 seconds
        if not self._recent_price_history or now - self._recent_price_history[-1][0] >= 10:
            self._recent_price_history.append((now, current_price))

        # STARTUP INITIALIZATION: Set conservative boundaries if we don't have them yet
        # OR if price has moved significantly since last boundary update
        price_change_threshold = Decimal('0.01')  # 1% price change threshold
        significant_price_change = False

        if self._safe_buy_ceiling != Decimal('0') and self._safe_sell_floor != Decimal('0'):
            # Calculate if price has changed significantly since boundaries were set
            boundary_mid = (self._safe_buy_ceiling + self._safe_sell_floor) / 2
            price_change = abs(current_price - boundary_mid) / boundary_mid
            if price_change > price_change_threshold:
                significant_price_change = True
                self.logger().info(
                    f"SAFETY: Significant price change detected ({price_change*100:.2f}%), "
                    f"updating boundaries from mid-price {boundary_mid:.8f} to current {current_price:.8f}"
                )

        if (self._safe_buy_ceiling == Decimal('0') or self._safe_sell_floor == Decimal('0') or
            significant_price_change):
            # Calculate strategy spreads to set compatible boundaries
            bid_spread_decimal = self._bid_spread / 100  # Convert percentage to decimal
            ask_spread_decimal = self._ask_spread / 100  # Convert percentage to decimal

            # Set boundaries that are compatible with the strategy's spread settings
            # For very tight spreads, use a multiple of the spread rather than a fixed minimum
            spread_buffer = max(bid_spread_decimal, ask_spread_decimal) * Decimal('2.0')  # 2x the largest spread as buffer
            min_buffer = self._min_profit_margin  # Minimum 0.2% buffer

            # For ultra-tight spreads, use permissive boundaries for own-token market making
            max_spread = max(bid_spread_decimal, ask_spread_decimal)
            if max_spread < Decimal('0.0001'):  # If spread < 0.01% (very tight - own token market making)
                # For own-token market making, we want to provide liquidity and support price
                # Use much more permissive boundaries that allow normal market making
                buy_buffer = Decimal('0.01')  # 1% buffer (allows buying close to current price)
                sell_buffer = Decimal('0.01')  # 1% buffer (allows selling close to current price)
                self.logger().info(
                    f"SAFETY: Own-token market making mode detected (spread: {max_spread*100:.4f}%). "
                    f"Using permissive 1% safety buffers to allow price support and liquidity provision."
                )
            elif max_spread < Decimal('0.001'):  # If spread < 0.1% (tight)
                # Use 10x spread-based buffer for tight spreads
                buy_buffer = spread_buffer * Decimal('10')
                sell_buffer = spread_buffer * Decimal('10')
                self.logger().info(f"SAFETY: Using 10x spread buffer ({buy_buffer*100:.4f}%) for tight spreads")
            else:
                # Use minimum buffer for wider spreads
                buy_buffer = max(spread_buffer, min_buffer)
                sell_buffer = max(spread_buffer, min_buffer)
                self.logger().info(f"SAFETY: Using minimum buffer ({max(buy_buffer, sell_buffer)*100:.3f}%) for wider spreads")

            # Set boundaries that allow normal market making but prevent extreme moves
            self._safe_buy_ceiling = current_price * (1 + buy_buffer)  # FIXED: Allow buying ABOVE current price for own-token MM
            self._safe_sell_floor = current_price * (1 - sell_buffer)  # FIXED: Allow selling BELOW current price for own-token MM
            self._current_price_trend = "NEUTRAL"
            self._trend_confidence = 0.5

            self.logger().info(
                f"SAFETY: Initialized market-making compatible boundaries - "
                f"Buy ceiling: {self._safe_buy_ceiling:.8f}, "
                f"Sell floor: {self._safe_sell_floor:.8f}, "
                f"Current price: {current_price:.8f}, "
                f"Bid spread: {self._bid_spread}%, Ask spread: {self._ask_spread}%, "
                f"Buffer used: {max(buy_buffer, sell_buffer)*100:.3f}%"
            )
            return

        # Only analyze trends every 30 seconds to avoid over-analysis
        if now - self._last_trend_analysis < self._boundary_update_interval:
            return

        self._last_trend_analysis = now

        try:
            # Need at least 6 data points (1 minute) for meaningful analysis
            if len(self._recent_price_history) < 6:
                self.logger().debug("SAFETY: Insufficient price history for trend analysis")
                return

            # Calculate price trend over different time windows
            prices = [price for _, price in self._recent_price_history]
            times = [t for t, _ in self._recent_price_history]

            # Short-term trend (last 5 minutes)
            short_term_start = max(0, len(prices) - 30)  # 30 * 10 seconds = 5 minutes
            short_term_prices = prices[short_term_start:]
            short_term_change = (short_term_prices[-1] - short_term_prices[0]) / short_term_prices[0]

            # Medium-term trend (last 15 minutes)
            medium_term_start = max(0, len(prices) - 90)  # 90 * 10 seconds = 15 minutes
            medium_term_prices = prices[medium_term_start:]
            medium_term_change = (medium_term_prices[-1] - medium_term_prices[0]) / medium_term_prices[0]

            # Long-term trend (last 30 minutes)
            long_term_start = max(0, len(prices) - 180)  # 180 * 10 seconds = 30 minutes
            long_term_prices = prices[long_term_start:]
            long_term_change = (long_term_prices[-1] - long_term_prices[0]) / long_term_prices[0]

            # Determine overall trend with confidence
            trend_signals = []
            if abs(short_term_change) > float(self._price_trend_threshold):
                trend_signals.append(("SHORT", "UP" if short_term_change > 0 else "DOWN", abs(short_term_change)))
            if abs(medium_term_change) > float(self._price_trend_threshold):
                trend_signals.append(("MEDIUM", "UP" if medium_term_change > 0 else "DOWN", abs(medium_term_change)))
            if abs(long_term_change) > float(self._price_trend_threshold):
                trend_signals.append(("LONG", "UP" if long_term_change > 0 else "DOWN", abs(long_term_change)))

            # Calculate trend consensus
            up_signals = [s for s in trend_signals if s[1] == "UP"]
            down_signals = [s for s in trend_signals if s[1] == "DOWN"]

            previous_trend = self._current_price_trend

            if len(up_signals) > len(down_signals):
                self._current_price_trend = "UPTREND"
                self._trend_confidence = min(1.0, len(up_signals) / 3.0)
            elif len(down_signals) > len(up_signals):
                self._current_price_trend = "DOWNTREND"
                self._trend_confidence = min(1.0, len(down_signals) / 3.0)
            else:
                self._current_price_trend = "NEUTRAL"
                self._trend_confidence = 0.5

            # Update safe trading boundaries based on trend
            self._update_safe_trading_boundaries(current_price, prices)

            # Log trend changes and boundary updates
            if previous_trend != self._current_price_trend or self._logging_options & self.OPTION_LOG_STATUS_REPORT:
                self.logger().info(
                    f"SAFETY: Price trend analysis - "
                    f"Trend: {self._current_price_trend} (confidence: {self._trend_confidence:.2f}), "
                    f"Short-term: {short_term_change*100:.2f}%, "
                    f"Medium-term: {medium_term_change*100:.2f}%, "
                    f"Long-term: {long_term_change*100:.2f}%, "
                    f"Safe buy ceiling: {self._safe_buy_ceiling:.8f}, "
                    f"Safe sell floor: {self._safe_sell_floor:.8f}"
                )

        except Exception as e:
            self.logger().error(f"SAFETY: Error in price trend analysis: {str(e)}", exc_info=True)
            # Set conservative boundaries on error
            self._safe_buy_ceiling = current_price * Decimal('0.995')  # 0.5% below current
            self._safe_sell_floor = current_price * Decimal('1.005')   # 0.5% above current

    def _update_safe_trading_boundaries(self, current_price: Decimal, recent_prices: list) -> None:
        """
        Update safe buy ceiling and sell floor based on recent price action.

        This prevents buying at local highs and selling at local lows.
        """
        try:
            # Calculate recent price statistics
            min_recent = min(recent_prices[-60:]) if len(recent_prices) >= 60 else min(recent_prices)  # Last 10 minutes
            max_recent = max(recent_prices[-60:]) if len(recent_prices) >= 60 else max(recent_prices)  # Last 10 minutes
            avg_recent = sum(recent_prices[-30:]) / len(recent_prices[-30:]) if len(recent_prices) >= 30 else current_price

            # Calculate volatility (standard deviation)
            if len(recent_prices) >= 10:
                price_changes = [float(recent_prices[i] - recent_prices[i-1]) / float(recent_prices[i-1])
                               for i in range(1, min(len(recent_prices), 60))]
                volatility = Decimal(str(np.std(price_changes))) if price_changes else Decimal('0.01')
            else:
                volatility = Decimal('0.01')  # Default 1% volatility

            # Set boundaries based on trend and volatility
            volatility_buffer = volatility * Decimal('2')  # 2x volatility buffer
            min_buffer = self._min_profit_margin  # Minimum 0.2% buffer

            if self._current_price_trend == "UPTREND":
                # In uptrend: be more conservative with buys, aggressive with sells
                buy_buffer = max(min_buffer, volatility_buffer * Decimal('1.5'))
                sell_buffer = max(min_buffer, volatility_buffer * Decimal('0.5'))

                # Don't buy above recent average + buffer
                self._safe_buy_ceiling = min(avg_recent * (1 - buy_buffer), current_price * Decimal('0.998'))
                # Sell above recent low + buffer
                self._safe_sell_floor = max(min_recent * (1 + sell_buffer), current_price * Decimal('1.002'))

            elif self._current_price_trend == "DOWNTREND":
                # In downtrend: aggressive with buys, conservative with sells
                buy_buffer = max(min_buffer, volatility_buffer * Decimal('0.5'))
                sell_buffer = max(min_buffer, volatility_buffer * Decimal('1.5'))

                # Buy below recent high - buffer
                self._safe_buy_ceiling = min(max_recent * (1 - buy_buffer), current_price * Decimal('0.998'))
                # Don't sell below recent average - buffer
                self._safe_sell_floor = max(avg_recent * (1 + sell_buffer), current_price * Decimal('1.002'))

            else:  # NEUTRAL
                # In neutral: balanced approach
                buffer = max(min_buffer, volatility_buffer)

                self._safe_buy_ceiling = current_price * (1 - buffer)
                self._safe_sell_floor = current_price * (1 + buffer)

            # Ensure boundaries make sense (buy ceiling < sell floor)
            if self._safe_buy_ceiling >= self._safe_sell_floor:
                # If boundaries overlap, create minimum spread
                mid_price = (self._safe_buy_ceiling + self._safe_sell_floor) / 2
                min_spread = self._min_profit_margin
                self._safe_buy_ceiling = mid_price * (1 - min_spread)
                self._safe_sell_floor = mid_price * (1 + min_spread)

            self.logger().debug(
                f"SAFETY: Updated boundaries - "
                f"Trend: {self._current_price_trend}, "
                f"Volatility: {volatility*100:.3f}%, "
                f"Recent range: {min_recent:.8f} - {max_recent:.8f}, "
                f"Avg: {avg_recent:.8f}, "
                f"Buy ceiling: {self._safe_buy_ceiling:.8f}, "
                f"Sell floor: {self._safe_sell_floor:.8f}"
            )

        except Exception as e:
            self.logger().error(f"SAFETY: Error updating trading boundaries: {str(e)}", exc_info=True)

    def _validate_buy_order_safety(self, buy_price: Decimal, current_price: Decimal) -> Tuple[bool, str]:
        """
        PRODUCTION SAFETY: Validate if a buy order is safe to prevent buying high.

        This is CRITICAL for preventing systematic losses in production.

        Args:
            buy_price: Proposed buy order price
            current_price: Current market price

        Returns:
            Tuple[bool, str]: (is_safe, rejection_reason)
        """
        if not self._enforce_buy_low_sell_high:
            return True, ""

        try:
            # Rule 1: Never buy above the safe buy ceiling
            if buy_price > self._safe_buy_ceiling:
                reason = (f"Buy price {buy_price:.8f} exceeds safe ceiling {self._safe_buy_ceiling:.8f} "
                         f"(trend: {self._current_price_trend})")
                return False, reason

            # Rule 2: In strong uptrends, be extra conservative
            if self._current_price_trend == "UPTREND" and self._trend_confidence > 0.7:
                conservative_ceiling = current_price * (1 - self._min_profit_margin * Decimal('2'))
                if buy_price > conservative_ceiling:
                    reason = (f"Buy price {buy_price:.8f} too high for strong uptrend "
                             f"(conservative ceiling: {conservative_ceiling:.8f})")
                    return False, reason

            # Rule 3: Check against recent price history
            if len(self._recent_price_history) >= 30:  # 5 minutes of data
                recent_prices = [price for _, price in self._recent_price_history[-30:]]
                recent_min = min(recent_prices)
                recent_avg = sum(recent_prices) / len(recent_prices)

                # Don't buy above recent average unless in downtrend
                if self._current_price_trend != "DOWNTREND" and buy_price > recent_avg:
                    reason = (f"Buy price {buy_price:.8f} above recent average {recent_avg:.8f} "
                             f"in {self._current_price_trend} market")
                    return False, reason

            # Rule 4: Ensure minimum profit potential (MODIFIED for own-token market making)
            # For own-token market making, we prioritize liquidity provision and price support over strict profit margins

            # Check if this is own-token market making scenario
            is_own_token_mm = (hasattr(self, '_enable_dex_coordination') and self._enable_dex_coordination)

            if not is_own_token_mm:
                # Regular market making - apply strict profit validation
                min_profitable_sell = buy_price * (1 + self._min_profit_margin)
                if min_profitable_sell > self._safe_sell_floor:
                    reason = (f"Buy at {buy_price:.8f} would require selling below safe floor "
                             f"{self._safe_sell_floor:.8f} for minimum profit")
                    return False, reason
            else:
                # Own-token market making - use relaxed profit validation
                # Allow buy orders that support price even if profit margin is tight
                self.logger().debug(
                    f"OWN-TOKEN MM: Allowing buy order at {buy_price:.8f} for price support "
                    f"(safe floor: {self._safe_sell_floor:.8f})"
                )

            return True, ""

        except Exception as e:
            self.logger().error(f"SAFETY: Error validating buy order: {str(e)}", exc_info=True)
            # Err on the side of caution
            return False, f"Validation error: {str(e)}"

    def _validate_sell_order_safety(self, sell_price: Decimal, current_price: Decimal) -> Tuple[bool, str]:
        """
        PRODUCTION SAFETY: Validate if a sell order is safe to prevent selling low.

        This is CRITICAL for preventing systematic losses in production.

        Args:
            sell_price: Proposed sell order price
            current_price: Current market price

        Returns:
            Tuple[bool, str]: (is_safe, rejection_reason)
        """
        if not self._enforce_buy_low_sell_high:
            return True, ""

        try:
            # TEMPORARY DEBUG: Allow sell orders below safe floor if they're above current price
            # This helps debug the root cause while still preventing truly bad trades
            if sell_price < current_price:
                reason = (f"Sell price {sell_price:.8f} below current price {current_price:.8f} - "
                         f"this would be selling below market!")
                return False, reason

            # Rule 1: Never sell below the safe sell floor (but allow if above current price for debugging)
            if sell_price < self._safe_sell_floor:
                # TEMPORARY: Log warning but allow if above current price
                if sell_price > current_price:
                    self.logger().warning(
                        f"SAFETY DEBUG: Allowing sell at {sell_price:.8f} below safe floor {self._safe_sell_floor:.8f} "
                        f"because it's above current price {current_price:.8f} (trend: {self._current_price_trend})"
                    )
                    # Continue with other checks instead of rejecting
                else:
                    reason = (f"Sell price {sell_price:.8f} below safe floor {self._safe_sell_floor:.8f} "
                             f"(trend: {self._current_price_trend})")
                    return False, reason

            # Rule 2: In strong downtrends, be extra conservative
            if self._current_price_trend == "DOWNTREND" and self._trend_confidence > 0.7:
                conservative_floor = current_price * (1 + self._min_profit_margin * Decimal('2'))
                if sell_price < conservative_floor:
                    reason = (f"Sell price {sell_price:.8f} too low for strong downtrend "
                             f"(conservative floor: {conservative_floor:.8f})")
                    return False, reason

            # Rule 3: Check against recent price history
            if len(self._recent_price_history) >= 30:  # 5 minutes of data
                recent_prices = [price for _, price in self._recent_price_history[-30:]]
                recent_max = max(recent_prices)
                recent_avg = sum(recent_prices) / len(recent_prices)

                # Don't sell below recent average unless in uptrend
                if self._current_price_trend != "UPTREND" and sell_price < recent_avg:
                    reason = (f"Sell price {sell_price:.8f} below recent average {recent_avg:.8f} "
                             f"in {self._current_price_trend} market")
                    return False, reason

            # Rule 4: Check against our own recent buy prices for profitability
            if len(self._buy_prices) > 0:
                # Get recent buy prices (last 10 trades or 1 hour, whichever is less)
                recent_buys = list(self._buy_prices)[-10:]
                if recent_buys:
                    avg_recent_buy = sum(recent_buys) / len(recent_buys)
                    min_profitable_sell = avg_recent_buy * (1 + self._min_profit_margin)

                    if sell_price < min_profitable_sell:
                        reason = (f"Sell price {sell_price:.8f} below profitable level {min_profitable_sell:.8f} "
                                 f"(avg recent buy: {avg_recent_buy:.8f})")
                        return False, reason

            return True, ""

        except Exception as e:
            self.logger().error(f"SAFETY: Error validating sell order: {str(e)}", exc_info=True)
            # Err on the side of caution
            return False, f"Validation error: {str(e)}"

    def _log_order_rejection(self, side: str, price: Decimal, reason: str) -> None:
        """
        Log order rejections for monitoring and analysis.
        """
        now = time.time()

        if side.upper() == "BUY":
            self._rejected_buy_orders += 1
        else:
            self._rejected_sell_orders += 1

        # Log individual rejections at debug level
        self.logger().debug(f"SAFETY: Rejected {side} order at {price:.8f} - {reason}")

        # Log summary statistics periodically
        if now - self._last_rejection_log > self._rejection_log_interval:
            total_rejections = self._rejected_buy_orders + self._rejected_sell_orders
            if total_rejections > 0:
                self.logger().warning(
                    f"SAFETY: Order rejection summary (last {self._rejection_log_interval/60:.1f} min) - "
                    f"Total: {total_rejections}, Buy: {self._rejected_buy_orders}, Sell: {self._rejected_sell_orders}. "
                    f"This indicates the safety system is actively preventing potentially unprofitable trades."
                )
                # Reset counters
                self._rejected_buy_orders = 0
                self._rejected_sell_orders = 0
            self._last_rejection_log = now

    def _log_order_event(self, event_type: str, order_id: str, trading_pair: str,
                        side: str, amount: str, price: str):
        """
        Log order events with consistent formatting

        Args:
            event_type: Type of order event (e.g., CREATED, FILLED, CANCELLED)
            order_id: The ID of the order
            trading_pair: The trading pair for the order
            side: BUY or SELL
            amount: Order amount
            price: Order price
        """
        log_entry = {
            'timestamp': time.time(),
            'event': event_type,
            'order_id': order_id,
            'trading_pair': trading_pair,
            'side': side,
            'amount': amount,
            'price': price
        }
        self._order_history.append(log_entry)
        self.logger().debug(f"[ORDER_EVENT] {log_entry}")

    def _update_avg_prices(self, is_buy: bool, price: Decimal):
        """
        Update average buy/sell prices with new price data

        Args:
            is_buy: Whether this is a buy order (True) or sell order (False)
            price: The price at which the order was filled
        """
        if price <= 0:
            self.logger().error(f"Invalid price {price} received for {'buy' if is_buy else 'sell'}")
            return

        now = time.time()

        try:
            # Always update the price history immediately
            if is_buy:
                self._buy_prices.append(price)
                if len(self._buy_prices) > 1000:  # Keep a reasonable history size
                    self._buy_prices.popleft()
            else:
                self._sell_prices.append(price)
                if len(self._sell_prices) > 1000:  # Keep a reasonable history size
                    self._sell_prices.popleft()

            # ANTI-DRAIN: Enhanced trade tracking with USD volume monitoring
            if self._enable_anti_drain_protection:
                # Calculate USD volume for this trade
                usd_volume = price * Decimal('1000')  # Assuming 1000 FULA per trade (adjust as needed)

                trade_info = {
                    'timestamp': now,
                    'is_buy': is_buy,
                    'price': price,
                    'side': 'BUY' if is_buy else 'SELL',
                    'usd_volume': usd_volume
                }
                self._recent_trades.append(trade_info)

                # Track USD volume with timestamp
                volume_entry = {
                    'timestamp': now,
                    'usd_volume': usd_volume,
                    'is_buy': is_buy
                }
                self._usd_volume_tracking.append(volume_entry)

                # Update cumulative volumes
                if is_buy:
                    self._cumulative_buy_volume_usd += usd_volume
                else:
                    self._cumulative_sell_volume_usd += usd_volume

                # Check for large orders
                if usd_volume >= self._large_order_threshold:
                    self._large_order_history.append(trade_info)
                    self.logger().warning(
                        f"ANTI-DRAIN: Large order detected - ${usd_volume:.2f} USD "
                        f"({'BUY' if is_buy else 'SELL'} at {price:.8f})"
                    )

                # Enhanced attack pattern detection
                self._check_enhanced_attack_patterns()

                # ADVERSE SELECTION PROTECTION: Track fill for momentum analysis
                fill_info = {
                    'timestamp': now,
                    'is_buy': is_buy,
                    'price': price,
                    'aggressive': False  # Will be updated based on price movement analysis
                }
                self._recent_fills.append(fill_info)

                # ADVERSE SELECTION PROTECTION: Update volatility and momentum
                self._update_volatility_and_momentum(price, now)

                # ORDER BOOK INTELLIGENCE & MARKET IMPACT: Update analysis
                self._update_orderbook_and_impact_analysis(now)

            # Only log updates every 10 seconds to reduce log spam
            if now - self._last_avg_update >= 10 and (self._logging_options & self.OPTION_LOG_MAKER_ORDER_FILLED):
                action = "BUY" if is_buy else "SELL"
                self.logger().debug(
                    f"Updated {action} price history - "
                    f"Price: {price:.8f}, "
                    f"Total {action} prices tracked: {len(self._buy_prices) if is_buy else len(self._sell_prices)}"
                )
                self._last_avg_update = now

        except Exception as e:
            self.logger().error(f"Error updating price history: {str(e)}", exc_info=True)

    def _check_enhanced_attack_patterns(self) -> None:
        """Enhanced attack pattern detection with USD volume analysis"""
        now = time.time()

        # 1. Check balance drain rate
        self._check_balance_drain_rate(now)

        # 2. Check USD volume patterns
        self._check_usd_volume_patterns(now)

        # 3. Check large order clustering
        self._check_large_order_clustering(now)

        # 4. Update alert level based on findings
        self._update_alert_level(now)

    def _check_balance_drain_rate(self, now: float) -> None:
        """Monitor actual balance drain rate"""
        if now - self._last_balance_check < 10:  # Check every 10 seconds
            return

        self._last_balance_check = now

        # Get current balances
        try:
            market = self._market_info.market
            current_base = market.get_available_balance(self.base_asset)
            current_quote = market.get_available_balance(self.quote_asset)

            # Initialize baseline if not set
            if self._initial_base_balance == 0:
                self._initial_base_balance = current_base
                self._initial_quote_balance = current_quote
                self.logger().info(
                    f"ANTI-DRAIN: Baseline balances set - "
                    f"Base: {current_base:.2f} {self.base_asset}, "
                    f"Quote: {current_quote:.2f} {self.quote_asset}"
                )
                return

            # Calculate drain percentages
            base_drain_pct = (self._initial_base_balance - current_base) / self._initial_base_balance
            quote_drain_pct = (self._initial_quote_balance - current_quote) / self._initial_quote_balance

            # Check for significant balance drain
            if base_drain_pct > self._balance_drain_threshold or quote_drain_pct > self._balance_drain_threshold:
                self.logger().warning(
                    f"ANTI-DRAIN: Significant balance drain detected! "
                    f"Base drain: {base_drain_pct*100:.1f}%, Quote drain: {quote_drain_pct*100:.1f}%"
                )

                # Emergency stop if drain is too high
                if base_drain_pct > self._max_balance_drain_pct or quote_drain_pct > self._max_balance_drain_pct:
                    self.logger().error(
                        f"ANTI-DRAIN: EMERGENCY STOP! Balance drain exceeded {self._max_balance_drain_pct*100:.0f}% threshold!"
                    )
                    self._attack_detected = True
                    self._attack_phase = "EMERGENCY"
                    self._attack_start_time = now

        except Exception as e:
            self.logger().error(f"Error checking balance drain: {str(e)}")

    def _check_usd_volume_patterns(self, now: float) -> None:
        """Check for suspicious USD volume patterns"""
        # Get recent USD volumes (last 5 minutes)
        recent_volumes = [v for v in self._usd_volume_tracking if now - v['timestamp'] <= 300]

        if len(recent_volumes) < 5:
            return

        # Calculate total USD volume in last 5 minutes
        total_usd_volume = sum(v['usd_volume'] for v in recent_volumes)
        buy_usd_volume = sum(v['usd_volume'] for v in recent_volumes if v['is_buy'])
        sell_usd_volume = sum(v['usd_volume'] for v in recent_volumes if not v['is_buy'])

        # Check for high volume activity
        if total_usd_volume > Decimal('1000'):  # $1000 in 5 minutes
            self.logger().warning(
                f"ANTI-DRAIN: High USD volume detected - ${total_usd_volume:.2f} in 5 minutes "
                f"(Buy: ${buy_usd_volume:.2f}, Sell: ${sell_usd_volume:.2f})"
            )

        # Check for imbalanced volume (potential drain)
        if sell_usd_volume > 0:
            volume_ratio = buy_usd_volume / sell_usd_volume
            if volume_ratio > 5:  # 5:1 buy ratio indicates potential pump
                self.logger().warning(f"ANTI-DRAIN: High buy volume ratio detected: {volume_ratio:.1f}:1")
            elif volume_ratio < 0.2:  # 1:5 sell ratio indicates potential dump
                self.logger().error(f"ANTI-DRAIN: High sell volume ratio detected: 1:{1/volume_ratio:.1f}")
                self._attack_detected = True
                self._attack_phase = "DUMP"
                self._attack_start_time = now

    def _check_large_order_clustering(self, now: float) -> None:
        """Check for clustering of large orders (potential coordinated attack)"""
        recent_large_orders = [o for o in self._large_order_history if now - o['timestamp'] <= 60]  # Last minute

        if len(recent_large_orders) >= 3:
            total_large_volume = sum(o['usd_volume'] for o in recent_large_orders)
            self.logger().warning(
                f"ANTI-DRAIN: Large order clustering detected - "
                f"{len(recent_large_orders)} large orders totaling ${total_large_volume:.2f} in 1 minute"
            )

            # If too many large orders, consider it an attack
            if len(recent_large_orders) >= 5:
                self.logger().error("ANTI-DRAIN: Coordinated large order attack detected!")
                self._attack_detected = True
                self._attack_phase = "COORDINATED"
                self._attack_start_time = now

    def _update_alert_level(self, now: float) -> None:
        """Update graduated alert level based on current conditions"""
        previous_level = self._alert_level

        # Get recent activity metrics
        recent_volumes = [v for v in self._usd_volume_tracking if now - v['timestamp'] <= 300]
        recent_large_orders = [o for o in self._large_order_history if now - o['timestamp'] <= 60]
        total_recent_volume = sum(v['usd_volume'] for v in recent_volumes)

        # Determine alert level
        if self._attack_detected:
            self._alert_level = "RED"
        elif len(recent_large_orders) >= 3 or total_recent_volume > Decimal('1000'):
            self._alert_level = "ORANGE"
        elif len(recent_large_orders) >= 1 or total_recent_volume > Decimal('500'):
            self._alert_level = "YELLOW"
        else:
            self._alert_level = "GREEN"

        # Log alert level changes
        if self._alert_level != previous_level:
            self.logger().info(
                f"ANTI-DRAIN: Alert level changed from {previous_level} to {self._alert_level} "
                f"(Volume: ${total_recent_volume:.2f}, Large orders: {len(recent_large_orders)})"
            )

    def _update_volatility_and_momentum(self, current_price: Decimal, now: float) -> None:
        """
        ADVERSE SELECTION PROTECTION: Update real-time volatility and momentum indicators

        This method calculates:
        1. Multi-timeframe volatility (1m, 5m, 15m)
        2. Volatility regime classification
        3. Price momentum and velocity
        4. Directional pressure indicators

        Args:
            current_price: Current market price
            now: Current timestamp
        """
        try:
            # Update volatility every 5 seconds to balance accuracy and performance
            if now - self._last_volatility_update < 5.0:
                return

            self._last_volatility_update = now

            # Calculate price change from last update
            if self._last_price_for_momentum > 0:
                price_change = (current_price - self._last_price_for_momentum) / self._last_price_for_momentum

                # Add to price change histories
                self._price_changes_1m.append((now, price_change))
                self._price_changes_5m.append((now, price_change))
                self._price_changes_15m.append((now, price_change))

                # Clean old data
                self._clean_old_price_changes(now)

                # Calculate volatilities for different timeframes
                self._calculate_multi_timeframe_volatility()

                # Determine volatility regime
                self._update_volatility_regime()

                # Calculate momentum indicators
                self._calculate_momentum_indicators(current_price, now)

                # Update spread multiplier based on volatility and momentum
                self._update_spread_multiplier()

            self._last_price_for_momentum = current_price

        except Exception as e:
            self.logger().error(f"Error updating volatility and momentum: {str(e)}")

    def _clean_old_price_changes(self, now: float) -> None:
        """Remove price changes older than their respective windows"""
        # Clean 1-minute data (keep last 60 seconds)
        while self._price_changes_1m and now - self._price_changes_1m[0][0] > 60:
            self._price_changes_1m.popleft()

        # Clean 5-minute data (keep last 300 seconds)
        while self._price_changes_5m and now - self._price_changes_5m[0][0] > 300:
            self._price_changes_5m.popleft()

        # Clean 15-minute data (keep last 900 seconds)
        while self._price_changes_15m and now - self._price_changes_15m[0][0] > 900:
            self._price_changes_15m.popleft()

    def _calculate_multi_timeframe_volatility(self) -> None:
        """
        ENHANCED VOLATILITY DETECTION: Calculate volatility for 1m, 5m, and 15m timeframes
        with improved statistical measures for FULA-USDT token market making
        """
        # Calculate 1-minute volatility with enhanced statistical measures
        if len(self._price_changes_1m) >= 5:
            changes_1m = [change for _, change in self._price_changes_1m]
            if changes_1m:
                # Standard volatility
                self._volatility_1m = Decimal(str(np.std(changes_1m)))

                # ENHANCEMENT: Add volatility trend detection
                if len(changes_1m) >= 10:
                    recent_vol = np.std(changes_1m[-5:])  # Last 5 changes
                    older_vol = np.std(changes_1m[-10:-5])  # Previous 5 changes
                    self._volatility_trend_1m = Decimal(str((recent_vol - older_vol) / older_vol)) if older_vol > 0 else Decimal('0')
                else:
                    self._volatility_trend_1m = Decimal('0')

                # ENHANCEMENT: Calculate volatility percentiles for regime detection
                self._volatility_percentile_90_1m = Decimal(str(np.percentile(np.abs(changes_1m), 90)))
                self._volatility_percentile_50_1m = Decimal(str(np.percentile(np.abs(changes_1m), 50)))
            else:
                self._volatility_1m = Decimal('0.01')
                self._volatility_trend_1m = Decimal('0')
                self._volatility_percentile_90_1m = Decimal('0.01')
                self._volatility_percentile_50_1m = Decimal('0.005')

        # Calculate 5-minute volatility with enhanced measures
        if len(self._price_changes_5m) >= 10:
            changes_5m = [change for _, change in self._price_changes_5m]
            if changes_5m:
                self._volatility_5m = Decimal(str(np.std(changes_5m)))

                # ENHANCEMENT: Volatility acceleration detection
                if len(changes_5m) >= 20:
                    recent_vol = np.std(changes_5m[-10:])
                    older_vol = np.std(changes_5m[-20:-10])
                    self._volatility_acceleration_5m = Decimal(str((recent_vol - older_vol) / older_vol)) if older_vol > 0 else Decimal('0')
                else:
                    self._volatility_acceleration_5m = Decimal('0')
            else:
                self._volatility_5m = Decimal('0.01')
                self._volatility_acceleration_5m = Decimal('0')

        # Calculate 15-minute volatility
        if len(self._price_changes_15m) >= 20:
            changes_15m = [change for _, change in self._price_changes_15m]
            self._volatility_15m = Decimal(str(np.std(changes_15m))) if changes_15m else Decimal('0.01')

    def _update_volatility_regime(self) -> None:
        """Classify current volatility regime based on multi-timeframe analysis"""
        # Use the highest volatility across timeframes for regime classification
        max_volatility = max(self._volatility_1m, self._volatility_5m, self._volatility_15m)

        previous_regime = self._volatility_regime

        if max_volatility < Decimal('0.005'):  # < 0.5%
            self._volatility_regime = "LOW"
        elif max_volatility < Decimal('0.015'):  # < 1.5%
            self._volatility_regime = "MEDIUM"
        elif max_volatility < Decimal('0.03'):  # < 3%
            self._volatility_regime = "HIGH"
        else:  # >= 3%
            self._volatility_regime = "EXTREME"

        # Log regime changes
        if self._volatility_regime != previous_regime:
            self.logger().info(
                f"ADVERSE_SELECTION: Volatility regime changed from {previous_regime} to {self._volatility_regime} "
                f"(1m: {self._volatility_1m*100:.2f}%, 5m: {self._volatility_5m*100:.2f}%, "
                f"15m: {self._volatility_15m*100:.2f}%)"
            )

    def _calculate_momentum_indicators(self, current_price: Decimal, now: float) -> None:
        """Calculate momentum indicators for adverse selection protection"""
        try:
            # Update momentum every 2 seconds for responsiveness
            if now - self._last_momentum_update < 2.0:
                return

            self._last_momentum_update = now

            # Calculate price velocity (rate of price change)
            if len(self._price_changes_1m) >= 3:
                recent_changes = [change for _, change in self._price_changes_1m[-3:]]
                self._price_velocity = sum(recent_changes) / len(recent_changes)

            # Calculate momentum indicator (similar to RSI but simpler)
            if len(self._price_changes_1m) >= 10:
                changes = [change for _, change in self._price_changes_1m[-10:]]
                positive_changes = [c for c in changes if c > 0]
                negative_changes = [c for c in changes if c < 0]

                if len(positive_changes) + len(negative_changes) > 0:
                    positive_strength = sum(positive_changes) if positive_changes else 0
                    negative_strength = abs(sum(negative_changes)) if negative_changes else 0

                    if positive_strength + negative_strength > 0:
                        # Momentum ranges from -1 (strong negative) to +1 (strong positive)
                        self._momentum_indicator = Decimal(str(
                            (positive_strength - negative_strength) / (positive_strength + negative_strength)
                        ))

            # Calculate directional pressure from recent fills
            self._calculate_directional_pressure(now)

            # Detect adverse momentum
            self._detect_adverse_momentum()

            # ENHANCEMENT: Multi-timeframe trend analysis
            self._analyze_multi_timeframe_trends(current_price, now)

            # ENHANCEMENT: Support/resistance level detection
            self._detect_support_resistance_levels(current_price)

            # ENHANCEMENT: Trend reversal detection
            self._detect_trend_reversals(current_price, now)

        except Exception as e:
            self.logger().error(f"Error calculating momentum indicators: {str(e)}")

    def _analyze_multi_timeframe_trends(self, current_price: Decimal, now: float) -> None:
        """
        ENHANCED PRICE TREND MONITORING: Analyze trends across multiple timeframes
        """
        try:
            # Initialize trend storage if needed
            if not hasattr(self, '_price_history_1m'):
                self._price_history_1m = deque(maxlen=60)  # 1 minute of prices
                self._price_history_5m = deque(maxlen=60)  # 5 minutes of prices (every 5s)
                self._price_history_15m = deque(maxlen=60)  # 15 minutes of prices (every 15s)
                self._last_price_record_1m = 0
                self._last_price_record_5m = 0
                self._last_price_record_15m = 0

            # Record prices at different intervals
            if now - self._last_price_record_1m >= 1:  # Every 1 second
                self._price_history_1m.append((now, current_price))
                self._last_price_record_1m = now

            if now - self._last_price_record_5m >= 5:  # Every 5 seconds
                self._price_history_5m.append((now, current_price))
                self._last_price_record_5m = now

            if now - self._last_price_record_15m >= 15:  # Every 15 seconds
                self._price_history_15m.append((now, current_price))
                self._last_price_record_15m = now

            # Calculate trends for each timeframe
            self._trend_1m = self._calculate_trend_direction(self._price_history_1m, 30)  # 30 second trend
            self._trend_5m = self._calculate_trend_direction(self._price_history_5m, 12)  # 1 minute trend
            self._trend_15m = self._calculate_trend_direction(self._price_history_15m, 20)  # 5 minute trend

            # Calculate trend strength
            self._trend_strength_1m = self._calculate_trend_strength(self._price_history_1m)
            self._trend_strength_5m = self._calculate_trend_strength(self._price_history_5m)
            self._trend_strength_15m = self._calculate_trend_strength(self._price_history_15m)

            # Determine overall trend consensus
            self._calculate_trend_consensus()

        except Exception as e:
            self.logger().error(f"Error analyzing multi-timeframe trends: {str(e)}")

    def _calculate_trend_direction(self, price_history, lookback: int) -> str:
        """Calculate trend direction for a given timeframe"""
        try:
            if len(price_history) < lookback:
                return "NEUTRAL"

            recent_prices = list(price_history)[-lookback:]
            if len(recent_prices) < 3:
                return "NEUTRAL"

            # Calculate linear regression slope
            x_values = list(range(len(recent_prices)))
            y_values = [float(price) for _, price in recent_prices]

            # Simple slope calculation
            n = len(recent_prices)
            sum_x = sum(x_values)
            sum_y = sum(y_values)
            sum_xy = sum(x * y for x, y in zip(x_values, y_values))
            sum_x2 = sum(x * x for x in x_values)

            slope = (n * sum_xy - sum_x * sum_y) / (n * sum_x2 - sum_x * sum_x)

            # Classify trend based on slope
            if slope > 0.0001:  # Positive slope threshold
                return "UPTREND"
            elif slope < -0.0001:  # Negative slope threshold
                return "DOWNTREND"
            else:
                return "NEUTRAL"

        except Exception as e:
            self.logger().error(f"Error calculating trend direction: {str(e)}")
            return "NEUTRAL"

    def _calculate_trend_strength(self, price_history) -> Decimal:
        """Calculate the strength of the trend (0 = weak, 1 = strong)"""
        try:
            if len(price_history) < 10:
                return Decimal('0')

            prices = [float(price) for _, price in price_history]

            # Calculate R-squared for trend strength
            x_values = list(range(len(prices)))
            n = len(prices)

            # Calculate means
            mean_x = sum(x_values) / n
            mean_y = sum(prices) / n

            # Calculate correlation coefficient
            numerator = sum((x - mean_x) * (y - mean_y) for x, y in zip(x_values, prices))
            sum_sq_x = sum((x - mean_x) ** 2 for x in x_values)
            sum_sq_y = sum((y - mean_y) ** 2 for y in prices)

            if sum_sq_x > 0 and sum_sq_y > 0:
                correlation = numerator / (sum_sq_x * sum_sq_y) ** 0.5
                r_squared = correlation ** 2
                return Decimal(str(r_squared))
            else:
                return Decimal('0')

        except Exception as e:
            self.logger().error(f"Error calculating trend strength: {str(e)}")
            return Decimal('0')

    def _calculate_trend_consensus(self) -> None:
        """Calculate consensus across multiple timeframes"""
        try:
            trends = [self._trend_1m, self._trend_5m, self._trend_15m]

            uptrend_count = trends.count("UPTREND")
            downtrend_count = trends.count("DOWNTREND")
            neutral_count = trends.count("NEUTRAL")

            # Determine consensus
            if uptrend_count >= 2:
                self._trend_consensus = "BULLISH"
            elif downtrend_count >= 2:
                self._trend_consensus = "BEARISH"
            else:
                self._trend_consensus = "NEUTRAL"

            # Calculate consensus strength
            max_count = max(uptrend_count, downtrend_count, neutral_count)
            self._trend_consensus_strength = Decimal(str(max_count / 3))

        except Exception as e:
            self.logger().error(f"Error calculating trend consensus: {str(e)}")
            self._trend_consensus = "NEUTRAL"
            self._trend_consensus_strength = Decimal('0')

    def _detect_support_resistance_levels(self, current_price: Decimal) -> None:
        """
        ENHANCED PRICE TREND MONITORING: Detect dynamic support and resistance levels
        """
        try:
            if not hasattr(self, '_price_history_5m') or len(self._price_history_5m) < 20:
                return

            # Get recent price data
            prices = [float(price) for _, price in self._price_history_5m]

            # Find local maxima and minima (potential support/resistance)
            resistance_levels = []
            support_levels = []

            # Look for peaks and troughs
            for i in range(2, len(prices) - 2):
                # Check for resistance (local maximum)
                if (prices[i] > prices[i-1] and prices[i] > prices[i-2] and
                    prices[i] > prices[i+1] and prices[i] > prices[i+2]):
                    resistance_levels.append(Decimal(str(prices[i])))

                # Check for support (local minimum)
                if (prices[i] < prices[i-1] and prices[i] < prices[i-2] and
                    prices[i] < prices[i+1] and prices[i] < prices[i+2]):
                    support_levels.append(Decimal(str(prices[i])))

            # Filter levels close to current price (within 2%)
            price_threshold = current_price * Decimal('0.02')

            nearby_resistance = [level for level in resistance_levels
                               if abs(level - current_price) <= price_threshold and level > current_price]
            nearby_support = [level for level in support_levels
                            if abs(level - current_price) <= price_threshold and level < current_price]

            # Store the closest levels
            self._nearest_resistance = min(nearby_resistance) if nearby_resistance else None
            self._nearest_support = max(nearby_support) if nearby_support else None

            # Calculate distance to levels
            if self._nearest_resistance:
                self._resistance_distance = (self._nearest_resistance - current_price) / current_price
            else:
                self._resistance_distance = Decimal('1')  # Far away

            if self._nearest_support:
                self._support_distance = (current_price - self._nearest_support) / current_price
            else:
                self._support_distance = Decimal('1')  # Far away

        except Exception as e:
            self.logger().error(f"Error detecting support/resistance levels: {str(e)}")

    def _detect_trend_reversals(self, current_price: Decimal, now: float) -> None:
        """
        ENHANCED PRICE TREND MONITORING: Detect potential trend reversals
        """
        try:
            # Initialize reversal tracking
            if not hasattr(self, '_reversal_signals'):
                self._reversal_signals = deque(maxlen=10)
                self._last_reversal_check = 0

            # Check every 10 seconds
            if now - self._last_reversal_check < 10:
                return

            self._last_reversal_check = now

            reversal_score = 0
            reversal_reasons = []

            # 1. Divergence between price and momentum
            if hasattr(self, '_momentum_indicator') and hasattr(self, '_price_velocity'):
                if (self._momentum_indicator > 0 and self._price_velocity < 0) or \
                   (self._momentum_indicator < 0 and self._price_velocity > 0):
                    reversal_score += 1
                    reversal_reasons.append("momentum_divergence")

            # 2. Approaching support/resistance levels
            if hasattr(self, '_resistance_distance') and self._resistance_distance < Decimal('0.005'):
                reversal_score += 1
                reversal_reasons.append("near_resistance")

            if hasattr(self, '_support_distance') and self._support_distance < Decimal('0.005'):
                reversal_score += 1
                reversal_reasons.append("near_support")

            # 3. Volatility spike (often precedes reversals)
            if hasattr(self, '_volatility_1m') and self._volatility_1m > Decimal('0.02'):
                reversal_score += 1
                reversal_reasons.append("volatility_spike")

            # 4. Volume anomaly
            if hasattr(self, '_volume_trend') and abs(self._volume_trend) > Decimal('0.5'):
                reversal_score += 1
                reversal_reasons.append("volume_anomaly")

            # 5. Trend consensus breakdown
            if hasattr(self, '_trend_consensus_strength') and self._trend_consensus_strength < Decimal('0.6'):
                reversal_score += 1
                reversal_reasons.append("consensus_breakdown")

            # Record reversal signal
            reversal_signal = {
                'timestamp': now,
                'score': reversal_score,
                'reasons': reversal_reasons,
                'price': current_price
            }
            self._reversal_signals.append(reversal_signal)

            # Determine reversal probability
            if reversal_score >= 3:
                self._reversal_probability = "HIGH"
                self.logger().warning(
                    f"TREND_REVERSAL: High reversal probability detected! "
                    f"Score: {reversal_score}/5, Reasons: {', '.join(reversal_reasons)}"
                )
            elif reversal_score >= 2:
                self._reversal_probability = "MEDIUM"
                self.logger().info(
                    f"TREND_REVERSAL: Medium reversal probability. "
                    f"Score: {reversal_score}/5, Reasons: {', '.join(reversal_reasons)}"
                )
            else:
                self._reversal_probability = "LOW"

        except Exception as e:
            self.logger().error(f"Error detecting trend reversals: {str(e)}")

    def _calculate_directional_pressure(self, now: float) -> None:
        """Calculate directional pressure from recent fills"""
        # Get recent fills (last 30 seconds)
        recent_fills = [fill for fill in self._recent_fills if now - fill['timestamp'] <= 30]

        if len(recent_fills) >= 3:
            buy_pressure = sum(1 for fill in recent_fills if fill['is_buy'])
            sell_pressure = sum(1 for fill in recent_fills if not fill['is_buy'])
            total_pressure = buy_pressure + sell_pressure

            if total_pressure > 0:
                # Directional pressure ranges from -1 (all sells) to +1 (all buys)
                self._directional_pressure = Decimal(str(
                    (buy_pressure - sell_pressure) / total_pressure
                ))

            # Calculate aggressive fill ratio (fills that moved price against us)
            aggressive_fills = sum(1 for fill in recent_fills if fill.get('aggressive', False))
            self._aggressive_fill_ratio = Decimal(str(aggressive_fills / len(recent_fills)))

    def _detect_adverse_momentum(self) -> None:
        """Detect if momentum is turning against us"""
        previous_state = self._adverse_momentum_detected

        # Adverse momentum conditions:
        # 1. Strong negative momentum (< -0.3)
        # 2. High negative directional pressure (< -0.5)
        # 3. High aggressive fill ratio (> 0.6)
        # 4. High short-term volatility

        adverse_conditions = 0

        if self._momentum_indicator < -self._momentum_threshold:
            adverse_conditions += 1

        if self._directional_pressure < -Decimal('0.5'):
            adverse_conditions += 1

        if self._aggressive_fill_ratio > Decimal('0.6'):
            adverse_conditions += 1

        if self._volatility_1m > Decimal('0.02'):  # > 2% volatility
            adverse_conditions += 1

        # Trigger adverse momentum if 2 or more conditions are met
        self._adverse_momentum_detected = adverse_conditions >= 2

        # Log momentum changes
        if self._adverse_momentum_detected != previous_state:
            if self._adverse_momentum_detected:
                self.logger().warning(
                    f"ADVERSE_SELECTION: Adverse momentum detected! "
                    f"Momentum: {self._momentum_indicator:.3f}, "
                    f"Pressure: {self._directional_pressure:.3f}, "
                    f"Aggressive fills: {self._aggressive_fill_ratio:.3f}, "
                    f"Volatility: {self._volatility_1m*100:.2f}%"
                )
            else:
                self.logger().info("ADVERSE_SELECTION: Adverse momentum cleared")

    def _update_spread_multiplier(self) -> None:
        """Update spread multiplier based on volatility regime and momentum"""
        previous_multiplier = self._base_spread_multiplier

        # Base multiplier based on volatility regime
        if self._volatility_regime == "LOW":
            base_multiplier = Decimal('0.8')  # Tighter spreads in low volatility
        elif self._volatility_regime == "MEDIUM":
            base_multiplier = Decimal('1.0')  # Normal spreads
        elif self._volatility_regime == "HIGH":
            base_multiplier = Decimal('2.0')  # Wider spreads in high volatility
        else:  # EXTREME
            base_multiplier = Decimal('4.0')  # Very wide spreads in extreme volatility

        # Adjust for adverse momentum
        if self._adverse_momentum_detected:
            momentum_multiplier = Decimal('1.5')  # 50% wider spreads during adverse momentum
        else:
            momentum_multiplier = Decimal('1.0')

        # Calculate final multiplier
        self._base_spread_multiplier = base_multiplier * momentum_multiplier

        # Ensure within bounds
        self._base_spread_multiplier = max(
            self._min_spread_multiplier,
            min(self._max_spread_multiplier, self._base_spread_multiplier)
        )

        # Log significant changes
        if abs(self._base_spread_multiplier - previous_multiplier) > Decimal('0.2'):
            self.logger().info(
                f"ADVERSE_SELECTION: Spread multiplier changed from {previous_multiplier:.2f}x to "
                f"{self._base_spread_multiplier:.2f}x (Regime: {self._volatility_regime}, "
                f"Adverse momentum: {self._adverse_momentum_detected})"
            )

    def _apply_adverse_selection_spread_adjustments(self, bid_spread: Decimal, ask_spread: Decimal) -> Tuple[Decimal, Decimal]:
        """
        Apply adverse selection protection by adjusting spreads based on volatility and momentum

        Args:
            bid_spread: Current bid spread
            ask_spread: Current ask spread

        Returns:
            Tuple of adjusted (bid_spread, ask_spread)
        """
        try:
            # Apply base spread multiplier from volatility and momentum analysis
            adjusted_bid_spread = bid_spread * self._base_spread_multiplier
            adjusted_ask_spread = ask_spread * self._base_spread_multiplier

            # Additional adjustments for specific conditions

            # 1. Extreme volatility: Pause market making temporarily
            if self._volatility_regime == "EXTREME":
                self.logger().warning(
                    f"ADVERSE_SELECTION: EXTREME volatility detected "
                    f"({self._volatility_1m*100:.2f}%). Pausing market making for safety."
                )
                # Return very wide spreads to effectively pause trading
                return bid_spread * Decimal('10'), ask_spread * Decimal('10')

            # 2. Adverse momentum: Asymmetric spread adjustment
            if self._adverse_momentum_detected:
                # If momentum is negative (price falling), widen bid spreads more
                if self._momentum_indicator < 0:
                    adjusted_bid_spread *= Decimal('1.3')  # 30% wider bid spreads
                    self.logger().debug(
                        f"ADVERSE_SELECTION: Widening bid spreads due to negative momentum "
                        f"({self._momentum_indicator:.3f})"
                    )
                # If momentum is positive but we're getting picked off, widen ask spreads
                elif self._aggressive_fill_ratio > Decimal('0.5'):
                    adjusted_ask_spread *= Decimal('1.3')  # 30% wider ask spreads
                    self.logger().debug(
                        f"ADVERSE_SELECTION: Widening ask spreads due to aggressive fills "
                        f"({self._aggressive_fill_ratio:.3f})"
                    )

            # 3. High directional pressure: Adjust spreads asymmetrically
            if abs(self._directional_pressure) > Decimal('0.6'):
                if self._directional_pressure > 0:  # Strong buy pressure
                    adjusted_ask_spread *= Decimal('1.2')  # Wider ask spreads
                else:  # Strong sell pressure
                    adjusted_bid_spread *= Decimal('1.2')  # Wider bid spreads

                self.logger().debug(
                    f"ADVERSE_SELECTION: Asymmetric spread adjustment for directional pressure "
                    f"({self._directional_pressure:.3f})"
                )

            # 4. Ensure minimum spreads for safety
            min_spread = self._minimum_spread if self._minimum_spread > 0 else Decimal('0.001')
            adjusted_bid_spread = max(adjusted_bid_spread, min_spread)
            adjusted_ask_spread = max(adjusted_ask_spread, min_spread)

            # Log significant spread changes
            if (abs(adjusted_bid_spread - bid_spread) > bid_spread * Decimal('0.2') or
                abs(adjusted_ask_spread - ask_spread) > ask_spread * Decimal('0.2')):

                self.logger().info(
                    f"ADVERSE_SELECTION: Spread adjustment - "
                    f"Bid: {bid_spread*100:.3f}% → {adjusted_bid_spread*100:.3f}%, "
                    f"Ask: {ask_spread*100:.3f}% → {adjusted_ask_spread*100:.3f}% "
                    f"(Regime: {self._volatility_regime}, Multiplier: {self._base_spread_multiplier:.2f}x)"
                )

            return adjusted_bid_spread, adjusted_ask_spread

        except Exception as e:
            self.logger().error(f"Error applying adverse selection spread adjustments: {str(e)}")
            # Return original spreads in case of error
            return bid_spread, ask_spread

    def _update_orderbook_and_impact_analysis(self, now: float) -> None:
        """
        Update order book intelligence and market impact analysis

        This method:
        1. Analyzes current order book structure
        2. Identifies support/resistance levels
        3. Calculates optimal order positioning
        4. Models our market impact
        5. Optimizes order sizes
        """
        try:
            # Update every 10 seconds to balance accuracy and performance
            if now - self._last_orderbook_analysis < 10.0:
                return

            self._last_orderbook_analysis = now

            # Get current order book
            market = self._market_info.market
            order_book = market.get_order_book(self.trading_pair)

            if order_book is None:
                return

            # Analyze order book structure
            self._analyze_order_book_structure(order_book, now)

            # Calculate market impact model
            self._update_market_impact_model(now)

            # Optimize order sizes based on impact
            self._optimize_order_sizes()

        except Exception as e:
            self.logger().error(f"Error updating orderbook and impact analysis: {str(e)}")

    def _analyze_order_book_structure(self, order_book, now: float) -> None:
        """
        ENHANCED ORDER BOOK ANALYSIS: Analyze order book structure for intelligent positioning
        with advanced metrics for FULA-USDT token market making
        """
        try:
            # Get bid and ask sides
            bids = order_book.bid_entries()
            asks = order_book.ask_entries()

            if not bids or not asks:
                return

            # Calculate current spread
            best_bid = bids[0].price
            best_ask = asks[0].price
            current_spread = (best_ask - best_bid) / best_bid

            # ENHANCEMENT: Calculate order book depth metrics
            self._calculate_enhanced_depth_metrics(bids, asks, best_bid, best_ask)

            # ENHANCEMENT: Detect order book patterns
            self._detect_order_book_patterns(bids, asks, now)

            # ENHANCEMENT: Calculate liquidity concentration
            self._calculate_liquidity_concentration(bids, asks)

            # Track spread history with enhanced metrics
            spread_entry = {
                'timestamp': now,
                'spread': current_spread,
                'bid': best_bid,
                'ask': best_ask,
                'bid_depth_5': sum(entry.amount for entry in bids[:5]),
                'ask_depth_5': sum(entry.amount for entry in asks[:5]),
                'bid_depth_10': sum(entry.amount for entry in bids[:10]) if len(bids) >= 10 else sum(entry.amount for entry in bids),
                'ask_depth_10': sum(entry.amount for entry in asks[:10]) if len(asks) >= 10 else sum(entry.amount for entry in asks),
                'spread_quality': self._calculate_spread_quality(current_spread),
                'book_pressure': self._calculate_book_pressure(bids, asks)
            }
            self._bid_ask_spread_history.append(spread_entry)

            # Calculate spread percentiles for adaptive positioning
            if len(self._bid_ask_spread_history) >= 20:
                recent_spreads = [entry['spread'] for entry in self._bid_ask_spread_history[-50:]]
                recent_spreads.sort()

                # Calculate 10th and 90th percentiles
                idx_10 = int(len(recent_spreads) * 0.1)
                idx_90 = int(len(recent_spreads) * 0.9)
                self._spread_percentile_10 = recent_spreads[idx_10] if idx_10 < len(recent_spreads) else recent_spreads[0]
                self._spread_percentile_90 = recent_spreads[idx_90] if idx_90 < len(recent_spreads) else recent_spreads[-1]

            # Analyze order book depth and find key levels
            self._find_support_resistance_levels(bids, asks, best_bid, best_ask)

            # Calculate order book imbalance
            self._calculate_order_book_imbalance(bids, asks)

            # Determine optimal order positioning
            self._calculate_optimal_order_levels(best_bid, best_ask, current_spread)

            # Store order book snapshot
            book_snapshot = {
                'timestamp': now,
                'best_bid': best_bid,
                'best_ask': best_ask,
                'spread': current_spread,
                'bid_depth': sum(entry.amount for entry in bids[:5]),  # Top 5 levels
                'ask_depth': sum(entry.amount for entry in asks[:5]),
                'imbalance': self._order_book_imbalance
            }
            self._order_book_levels.append(book_snapshot)

        except Exception as e:
            self.logger().error(f"Error analyzing order book structure: {str(e)}")

    def _find_support_resistance_levels(self, bids, asks, best_bid: Decimal, best_ask: Decimal) -> None:
        """Identify key support and resistance levels in the order book"""
        try:
            support_levels = []
            resistance_levels = []

            # Analyze bid side for support levels (large orders)
            for bid in bids[:20]:  # Look at top 20 bid levels
                if bid.amount * bid.price >= self._book_depth_threshold:
                    support_levels.append({
                        'price': bid.price,
                        'volume': bid.amount,
                        'strength': bid.amount * bid.price,
                        'distance_from_mid': abs(bid.price - best_bid) / best_bid
                    })

            # Analyze ask side for resistance levels (large orders)
            for ask in asks[:20]:  # Look at top 20 ask levels
                if ask.amount * ask.price >= self._book_depth_threshold:
                    resistance_levels.append({
                        'price': ask.price,
                        'volume': ask.amount,
                        'strength': ask.amount * ask.price,
                        'distance_from_mid': abs(ask.price - best_ask) / best_ask
                    })

            # Sort by strength and keep top levels
            support_levels.sort(key=lambda x: x['strength'], reverse=True)
            resistance_levels.sort(key=lambda x: x['strength'], reverse=True)

            # Update support/resistance levels
            self._support_resistance_levels = {
                'support': support_levels[:5],  # Top 5 support levels
                'resistance': resistance_levels[:5],  # Top 5 resistance levels
                'timestamp': time.time()
            }

            # Log significant levels
            if support_levels or resistance_levels:
                self.logger().debug(
                    f"ORDER_BOOK: Found {len(support_levels)} support and "
                    f"{len(resistance_levels)} resistance levels"
                )

        except Exception as e:
            self.logger().error(f"Error finding support/resistance levels: {str(e)}")

    def _calculate_order_book_imbalance(self, bids, asks) -> None:
        """Calculate order book imbalance for positioning bias"""
        try:
            # Calculate volume in top 5 levels on each side
            bid_volume = sum(entry.amount for entry in bids[:5])
            ask_volume = sum(entry.amount for entry in asks[:5])

            total_volume = bid_volume + ask_volume
            if total_volume > 0:
                # Imbalance ranges from -1 (all asks) to +1 (all bids)
                self._order_book_imbalance = (bid_volume - ask_volume) / total_volume
            else:
                self._order_book_imbalance = Decimal('0')

        except Exception as e:
            self.logger().error(f"Error calculating order book imbalance: {str(e)}")

    def _calculate_enhanced_depth_metrics(self, bids, asks, best_bid: Decimal, best_ask: Decimal) -> None:
        """
        ENHANCED ORDER BOOK ANALYSIS: Calculate advanced depth metrics for better positioning
        """
        try:
            # Calculate depth at different price levels
            price_levels = [0.001, 0.002, 0.005, 0.01, 0.02]  # 0.1%, 0.2%, 0.5%, 1%, 2%

            self._depth_metrics = {}

            for level in price_levels:
                bid_threshold = best_bid * (Decimal('1') - Decimal(str(level)))
                ask_threshold = best_ask * (Decimal('1') + Decimal(str(level)))

                bid_depth = sum(entry.amount for entry in bids if entry.price >= bid_threshold)
                ask_depth = sum(entry.amount for entry in asks if entry.price <= ask_threshold)

                self._depth_metrics[f'bid_depth_{int(level*10000)}bp'] = bid_depth
                self._depth_metrics[f'ask_depth_{int(level*10000)}bp'] = ask_depth
                self._depth_metrics[f'total_depth_{int(level*10000)}bp'] = bid_depth + ask_depth

            # Calculate average order size
            if len(bids) >= 5:
                self._avg_bid_size = sum(entry.amount for entry in bids[:5]) / 5
            if len(asks) >= 5:
                self._avg_ask_size = sum(entry.amount for entry in asks[:5]) / 5

        except Exception as e:
            self.logger().error(f"Error calculating enhanced depth metrics: {str(e)}")

    def _detect_order_book_patterns(self, bids, asks, now: float) -> None:
        """
        ENHANCED ORDER BOOK ANALYSIS: Detect patterns like walls, gaps, and clustering
        """
        try:
            # Detect bid/ask walls (large orders)
            wall_threshold = Decimal('5000')  # Adjust for FULA token

            bid_walls = [entry for entry in bids[:10] if entry.amount > wall_threshold]
            ask_walls = [entry for entry in asks[:10] if entry.amount > wall_threshold]

            self._bid_walls = len(bid_walls)
            self._ask_walls = len(ask_walls)

            # Detect price gaps (large spreads between levels)
            if len(bids) >= 3 and len(asks) >= 3:
                bid_gaps = []
                ask_gaps = []

                for i in range(1, min(3, len(bids))):
                    gap = (bids[i-1].price - bids[i].price) / bids[i].price
                    bid_gaps.append(gap)

                for i in range(1, min(3, len(asks))):
                    gap = (asks[i].price - asks[i-1].price) / asks[i-1].price
                    ask_gaps.append(gap)

                self._max_bid_gap = max(bid_gaps) if bid_gaps else Decimal('0')
                self._max_ask_gap = max(ask_gaps) if ask_gaps else Decimal('0')

            # Track pattern history
            pattern_entry = {
                'timestamp': now,
                'bid_walls': self._bid_walls,
                'ask_walls': self._ask_walls,
                'max_bid_gap': self._max_bid_gap,
                'max_ask_gap': self._max_ask_gap
            }

            if not hasattr(self, '_pattern_history'):
                self._pattern_history = deque(maxlen=100)
            self._pattern_history.append(pattern_entry)

        except Exception as e:
            self.logger().error(f"Error detecting order book patterns: {str(e)}")

    def _calculate_liquidity_concentration(self, bids, asks) -> None:
        """
        ENHANCED ORDER BOOK ANALYSIS: Calculate how concentrated liquidity is
        """
        try:
            # Calculate Herfindahl-Hirschman Index for liquidity concentration
            if len(bids) >= 5:
                total_bid_volume = sum(entry.amount for entry in bids[:10])
                if total_bid_volume > 0:
                    bid_shares = [(entry.amount / total_bid_volume) ** 2 for entry in bids[:10]]
                    self._bid_concentration = sum(bid_shares)
                else:
                    self._bid_concentration = Decimal('1')

            if len(asks) >= 5:
                total_ask_volume = sum(entry.amount for entry in asks[:10])
                if total_ask_volume > 0:
                    ask_shares = [(entry.amount / total_ask_volume) ** 2 for entry in asks[:10]]
                    self._ask_concentration = sum(ask_shares)
                else:
                    self._ask_concentration = Decimal('1')

        except Exception as e:
            self.logger().error(f"Error calculating liquidity concentration: {str(e)}")

    def _calculate_spread_quality(self, current_spread: Decimal) -> str:
        """
        ENHANCED ORDER BOOK ANALYSIS: Classify spread quality for positioning decisions
        """
        try:
            if current_spread < Decimal('0.001'):  # < 0.1%
                return "TIGHT"
            elif current_spread < Decimal('0.003'):  # < 0.3%
                return "NORMAL"
            elif current_spread < Decimal('0.01'):  # < 1%
                return "WIDE"
            else:
                return "VERY_WIDE"
        except:
            return "UNKNOWN"

    def _calculate_book_pressure(self, bids, asks) -> Decimal:
        """
        ENHANCED ORDER BOOK ANALYSIS: Calculate directional pressure from order book
        """
        try:
            # Calculate weighted pressure based on size and proximity to mid
            if not bids or not asks:
                return Decimal('0')

            mid_price = (bids[0].price + asks[0].price) / 2

            bid_pressure = Decimal('0')
            ask_pressure = Decimal('0')

            # Weight orders by size and proximity to mid price
            for entry in bids[:5]:
                distance = abs(entry.price - mid_price) / mid_price
                weight = entry.amount / (1 + distance * 10)  # Closer orders have more weight
                bid_pressure += weight

            for entry in asks[:5]:
                distance = abs(entry.price - mid_price) / mid_price
                weight = entry.amount / (1 + distance * 10)
                ask_pressure += weight

            total_pressure = bid_pressure + ask_pressure
            if total_pressure > 0:
                # Returns -1 to +1, where +1 means strong bid pressure
                return (bid_pressure - ask_pressure) / total_pressure
            else:
                return Decimal('0')

        except Exception as e:
            self.logger().error(f"Error calculating book pressure: {str(e)}")
            return Decimal('0')

    def _calculate_optimal_order_levels(self, best_bid: Decimal, best_ask: Decimal, current_spread: Decimal) -> None:
        """Calculate optimal bid and ask levels based on book analysis"""
        try:
            mid_price = (best_bid + best_ask) / 2

            # Base positioning: slightly inside the spread
            base_bid_offset = current_spread * Decimal('0.3')  # 30% into the spread
            base_ask_offset = current_spread * Decimal('0.3')

            # Adjust based on order book imbalance
            if self._order_book_imbalance > Decimal('0.2'):  # More bids than asks
                # Market is bullish, place ask closer to mid, bid further
                base_ask_offset *= Decimal('0.8')  # Closer ask
                base_bid_offset *= Decimal('1.2')  # Further bid
            elif self._order_book_imbalance < Decimal('-0.2'):  # More asks than bids
                # Market is bearish, place bid closer to mid, ask further
                base_bid_offset *= Decimal('0.8')  # Closer bid
                base_ask_offset *= Decimal('1.2')  # Further ask

            # Adjust based on spread percentiles
            if current_spread < self._spread_percentile_10:
                # Spread is very tight, be more aggressive
                base_bid_offset *= Decimal('0.7')
                base_ask_offset *= Decimal('0.7')
            elif current_spread > self._spread_percentile_90:
                # Spread is very wide, be more conservative
                base_bid_offset *= Decimal('1.3')
                base_ask_offset *= Decimal('1.3')

            # Calculate optimal levels
            self._optimal_bid_level = best_bid + base_bid_offset
            self._optimal_ask_level = best_ask - base_ask_offset

            # Ensure we don't cross the spread
            if self._optimal_bid_level >= self._optimal_ask_level:
                self._optimal_bid_level = mid_price - current_spread * Decimal('0.1')
                self._optimal_ask_level = mid_price + current_spread * Decimal('0.1')

        except Exception as e:
            self.logger().error(f"Error calculating optimal order levels: {str(e)}")

    def _update_market_impact_model(self, now: float) -> None:
        """Update market impact model based on our trading activity"""
        try:
            # Update every 30 seconds
            if now - self._last_impact_calculation < 30.0:
                return

            self._last_impact_calculation = now

            # Calculate our market share
            self._calculate_market_share()

            # Build price impact curve
            self._build_price_impact_curve()

            # Update volume participation limits
            self._update_volume_participation_limits()

        except Exception as e:
            self.logger().error(f"Error updating market impact model: {str(e)}")

    def _calculate_market_share(self) -> None:
        """Calculate our estimated market share"""
        try:
            # Get recent volume data from order book snapshots
            if len(self._order_book_levels) < 10:
                return

            # Estimate market volume from spread changes and depth
            recent_snapshots = list(self._order_book_levels)[-20:]  # Last 20 snapshots

            # Calculate average daily volume estimate
            total_depth = sum(
                snapshot['bid_depth'] + snapshot['ask_depth']
                for snapshot in recent_snapshots
            )
            avg_depth = total_depth / len(recent_snapshots)

            # Rough estimate: daily volume = average depth * turnover factor
            turnover_factor = Decimal('50')  # Assume depth turns over 50x per day
            self._estimated_daily_volume = avg_depth * turnover_factor

            # Calculate our market share
            if self._estimated_daily_volume > 0:
                self._our_market_share = self._our_daily_volume / self._estimated_daily_volume
                # Cap at reasonable maximum
                self._our_market_share = min(self._our_market_share, Decimal('0.3'))  # Max 30%

            self.logger().debug(
                f"MARKET_IMPACT: Estimated daily volume: {self._estimated_daily_volume:.0f}, "
                f"Our volume: {self._our_daily_volume:.0f}, "
                f"Market share: {self._our_market_share*100:.1f}%"
            )

        except Exception as e:
            self.logger().error(f"Error calculating market share: {str(e)}")

    def _build_price_impact_curve(self) -> None:
        """Build a curve mapping order size to expected price impact"""
        try:
            # Get recent order book data
            if not self._order_book_levels:
                return

            recent_snapshot = self._order_book_levels[-1]
            bid_depth = recent_snapshot['bid_depth']
            ask_depth = recent_snapshot['ask_depth']
            current_spread = recent_snapshot['spread']

            # Build impact curve for different order sizes
            base_order_size = self._order_amount

            # Test different order size multipliers
            size_multipliers = [0.1, 0.25, 0.5, 1.0, 2.0, 5.0, 10.0]

            for multiplier in size_multipliers:
                test_size = base_order_size * Decimal(str(multiplier))

                # Estimate impact based on order size vs available depth
                # For buy orders (consuming ask depth)
                ask_impact = self._estimate_depth_impact(test_size, ask_depth, current_spread)

                # For sell orders (consuming bid depth)
                bid_impact = self._estimate_depth_impact(test_size, bid_depth, current_spread)

                # Store in impact curve
                self._size_impact_curve[float(test_size)] = {
                    'buy_impact': ask_impact,
                    'sell_impact': bid_impact,
                    'avg_impact': (ask_impact + bid_impact) / 2
                }

            # Find optimal order size (largest size with acceptable impact)
            acceptable_sizes = [
                size for size, impacts in self._size_impact_curve.items()
                if impacts['avg_impact'] <= self._max_single_order_impact
            ]

            if acceptable_sizes:
                self._optimal_order_size = Decimal(str(max(acceptable_sizes)))
            else:
                # If no size is acceptable, use smallest size
                self._optimal_order_size = base_order_size * Decimal('0.1')

            self.logger().debug(
                f"MARKET_IMPACT: Optimal order size: {self._optimal_order_size:.2f} "
                f"(max impact: {self._max_single_order_impact*100:.1f}%)"
            )

        except Exception as e:
            self.logger().error(f"Error building price impact curve: {str(e)}")

    def _estimate_depth_impact(self, order_size: Decimal, available_depth: Decimal, spread: Decimal) -> Decimal:
        """Estimate price impact of an order given available depth"""
        try:
            if available_depth <= 0:
                return spread  # Full spread impact if no depth

            # Calculate what fraction of available depth we would consume
            depth_fraction = order_size / available_depth

            # Impact model: linear up to 50% of depth, then exponential
            if depth_fraction <= Decimal('0.5'):
                # Linear impact up to 50% of depth
                impact = spread * depth_fraction * Decimal('0.5')  # 50% of spread at 50% depth
            else:
                # Exponential impact beyond 50% of depth
                excess_fraction = depth_fraction - Decimal('0.5')
                linear_impact = spread * Decimal('0.25')  # 25% impact at 50% depth
                exponential_impact = spread * excess_fraction * Decimal('2')  # Exponential scaling
                impact = linear_impact + exponential_impact

            # Cap impact at 100% of spread
            return min(impact, spread)

        except Exception as e:
            self.logger().error(f"Error estimating depth impact: {str(e)}")
            return spread * Decimal('0.1')  # Default 10% impact

    def _update_volume_participation_limits(self) -> None:
        """
        ENHANCED VOLUME-BASED ADJUSTMENTS: Update volume participation limits
        based on recent market activity with advanced volume analysis for FULA-USDT
        """
        try:
            # Calculate recent volume from order book snapshots
            if len(self._order_book_levels) < 5:
                return

            # Get volume estimate from recent snapshots (last 5 minutes)
            recent_snapshots = [
                snapshot for snapshot in self._order_book_levels
                if time.time() - snapshot['timestamp'] <= 300  # 5 minutes
            ]

            if not recent_snapshots:
                return

            # ENHANCEMENT: Advanced volume pattern detection
            self._analyze_volume_patterns(recent_snapshots)

            # ENHANCEMENT: Calculate volume velocity and acceleration
            self._calculate_volume_velocity(recent_snapshots)

            # ENHANCEMENT: Detect volume anomalies
            volume_anomaly = self._detect_volume_anomalies(recent_snapshots)

            # Estimate recent volume from depth changes
            total_depth_changes = sum(
                abs(snapshot['bid_depth'] + snapshot['ask_depth'] -
                    (recent_snapshots[0]['bid_depth'] + recent_snapshots[0]['ask_depth']))
                for snapshot in recent_snapshots[1:]
            )

            # ENHANCEMENT: Calculate volume-based spread adjustments
            volume_multiplier = self._calculate_volume_spread_multiplier(total_depth_changes, volume_anomaly)

            # Estimate 5-minute volume
            estimated_5min_volume = total_depth_changes / len(recent_snapshots)

            # Calculate maximum order size based on participation rate
            max_participation_size = estimated_5min_volume * self._volume_participation_rate

            # Update optimal order size if participation limit is more restrictive
            if max_participation_size < self._optimal_order_size:
                self._optimal_order_size = max_participation_size
                self.logger().info(
                    f"MARKET_IMPACT: Order size limited by volume participation "
                    f"({self._volume_participation_rate*100:.0f}% of recent volume): "
                    f"{self._optimal_order_size:.2f}"
                )

        except Exception as e:
            self.logger().error(f"Error updating volume participation limits: {str(e)}")

    def _analyze_volume_patterns(self, recent_snapshots) -> None:
        """
        ENHANCED VOLUME-BASED ADJUSTMENTS: Analyze volume patterns for better decision making
        """
        try:
            if len(recent_snapshots) < 10:
                return

            # Calculate volume trend over time
            volumes = []
            timestamps = []

            for i in range(1, len(recent_snapshots)):
                prev_snapshot = recent_snapshots[i-1]
                curr_snapshot = recent_snapshots[i]

                volume_change = abs(
                    (curr_snapshot['bid_depth'] + curr_snapshot['ask_depth']) -
                    (prev_snapshot['bid_depth'] + prev_snapshot['ask_depth'])
                )
                volumes.append(volume_change)
                timestamps.append(curr_snapshot['timestamp'])

            if len(volumes) >= 5:
                # Calculate volume trend (increasing/decreasing)
                recent_avg = sum(volumes[-3:]) / 3
                older_avg = sum(volumes[-6:-3]) / 3 if len(volumes) >= 6 else recent_avg

                self._volume_trend = (recent_avg - older_avg) / older_avg if older_avg > 0 else Decimal('0')

                # Calculate volume consistency (low variance = consistent)
                volume_variance = Decimal(str(np.var(volumes)))
                volume_mean = Decimal(str(np.mean(volumes)))
                self._volume_consistency = volume_variance / volume_mean if volume_mean > 0 else Decimal('1')

        except Exception as e:
            self.logger().error(f"Error analyzing volume patterns: {str(e)}")

    def _calculate_volume_velocity(self, recent_snapshots) -> None:
        """
        ENHANCED VOLUME-BASED ADJUSTMENTS: Calculate volume velocity and acceleration
        """
        try:
            if len(recent_snapshots) < 6:
                return

            # Calculate volume velocity (rate of volume change)
            time_windows = [60, 180, 300]  # 1min, 3min, 5min

            for window in time_windows:
                window_snapshots = [
                    s for s in recent_snapshots
                    if time.time() - s['timestamp'] <= window
                ]

                if len(window_snapshots) >= 3:
                    total_volume = sum(
                        s['bid_depth'] + s['ask_depth'] for s in window_snapshots
                    )
                    avg_volume = total_volume / len(window_snapshots)

                    # Store velocity for different timeframes
                    setattr(self, f'_volume_velocity_{window}s', avg_volume)

            # Calculate volume acceleration (change in velocity)
            if hasattr(self, '_volume_velocity_60s') and hasattr(self, '_volume_velocity_180s'):
                if self._volume_velocity_180s > 0:
                    self._volume_acceleration = (
                        self._volume_velocity_60s - self._volume_velocity_180s
                    ) / self._volume_velocity_180s
                else:
                    self._volume_acceleration = Decimal('0')

        except Exception as e:
            self.logger().error(f"Error calculating volume velocity: {str(e)}")

    def _detect_volume_anomalies(self, recent_snapshots) -> bool:
        """
        ENHANCED VOLUME-BASED ADJUSTMENTS: Detect unusual volume patterns
        """
        try:
            if len(recent_snapshots) < 20:
                return False

            # Calculate recent volume vs historical average
            recent_volumes = []
            historical_volumes = []

            cutoff_time = time.time() - 120  # Last 2 minutes vs older

            for snapshot in recent_snapshots:
                volume = snapshot['bid_depth'] + snapshot['ask_depth']
                if snapshot['timestamp'] > cutoff_time:
                    recent_volumes.append(volume)
                else:
                    historical_volumes.append(volume)

            if len(recent_volumes) >= 3 and len(historical_volumes) >= 3:
                recent_avg = sum(recent_volumes) / len(recent_volumes)
                historical_avg = sum(historical_volumes) / len(historical_volumes)

                # Anomaly if recent volume is 2x or 0.5x historical
                if historical_avg > 0:
                    volume_ratio = recent_avg / historical_avg
                    if volume_ratio > 2 or volume_ratio < 0.5:
                        self.logger().warning(
                            f"VOLUME_ANOMALY: Recent volume {volume_ratio:.2f}x historical average"
                        )
                        return True

            return False

        except Exception as e:
            self.logger().error(f"Error detecting volume anomalies: {str(e)}")
            return False

    def _calculate_volume_spread_multiplier(self, total_depth_changes: Decimal, volume_anomaly: bool) -> Decimal:
        """
        ENHANCED VOLUME-BASED ADJUSTMENTS: Calculate spread multiplier based on volume analysis
        """
        try:
            base_multiplier = Decimal('1.0')

            # Adjust based on volume trend
            if hasattr(self, '_volume_trend'):
                if self._volume_trend > Decimal('0.5'):  # Strong increasing volume
                    base_multiplier *= Decimal('1.2')  # Widen spreads
                elif self._volume_trend < Decimal('-0.3'):  # Decreasing volume
                    base_multiplier *= Decimal('0.9')  # Tighten spreads

            # Adjust based on volume consistency
            if hasattr(self, '_volume_consistency'):
                if self._volume_consistency > Decimal('2'):  # High variance = inconsistent
                    base_multiplier *= Decimal('1.15')  # Widen spreads for safety

            # Adjust for volume anomalies
            if volume_anomaly:
                base_multiplier *= Decimal('1.3')  # Significantly widen spreads

            # Adjust based on volume acceleration
            if hasattr(self, '_volume_acceleration'):
                if self._volume_acceleration > Decimal('0.3'):  # Accelerating volume
                    base_multiplier *= Decimal('1.1')

            # Cap the multiplier
            return min(max(base_multiplier, Decimal('0.7')), Decimal('2.0'))

        except Exception as e:
            self.logger().error(f"Error calculating volume spread multiplier: {str(e)}")
            return Decimal('1.0')

    def _optimize_order_sizes(self) -> None:
        """Optimize order sizes based on market impact analysis"""
        try:
            # Ensure optimal order size is within reasonable bounds
            min_order_size = self._order_amount * Decimal('0.1')  # Minimum 10% of base
            max_order_size = self._order_amount * Decimal('5.0')   # Maximum 500% of base

            self._optimal_order_size = max(min_order_size,
                                         min(max_order_size, self._optimal_order_size))

            # Log significant changes
            size_ratio = self._optimal_order_size / self._order_amount
            if abs(size_ratio - Decimal('1.0')) > Decimal('0.2'):  # >20% change
                self.logger().info(
                    f"MARKET_IMPACT: Optimal order size adjusted to "
                    f"{self._optimal_order_size:.2f} ({size_ratio:.1f}x base size) "
                    f"based on market impact analysis"
                )

        except Exception as e:
            self.logger().error(f"Error optimizing order sizes: {str(e)}")

    def _apply_orderbook_intelligence(self, bid_spread: Decimal, ask_spread: Decimal, current_price: Decimal) -> Tuple[Decimal, Decimal]:
        """
        Apply order book intelligence and market impact modeling to optimize spreads and positioning

        Args:
            bid_spread: Current bid spread
            ask_spread: Current ask spread
            current_price: Current market price

        Returns:
            Tuple of optimized (bid_spread, ask_spread)
        """
        try:
            # Apply order book imbalance adjustments
            adjusted_bid_spread, adjusted_ask_spread = self._adjust_for_order_book_imbalance(
                bid_spread, ask_spread
            )

            # Apply support/resistance level positioning
            adjusted_bid_spread, adjusted_ask_spread = self._adjust_for_support_resistance(
                adjusted_bid_spread, adjusted_ask_spread, current_price
            )

            # Apply spread percentile optimization
            adjusted_bid_spread, adjusted_ask_spread = self._adjust_for_spread_percentiles(
                adjusted_bid_spread, adjusted_ask_spread
            )

            # Apply market impact considerations
            adjusted_bid_spread, adjusted_ask_spread = self._adjust_for_market_impact(
                adjusted_bid_spread, adjusted_ask_spread
            )

            # Log significant adjustments
            if (abs(adjusted_bid_spread - bid_spread) > bid_spread * Decimal('0.1') or
                abs(adjusted_ask_spread - ask_spread) > ask_spread * Decimal('0.1')):

                self.logger().info(
                    f"ORDER_BOOK_INTELLIGENCE: Spread optimization - "
                    f"Bid: {bid_spread*100:.3f}% → {adjusted_bid_spread*100:.3f}%, "
                    f"Ask: {ask_spread*100:.3f}% → {adjusted_ask_spread*100:.3f}% "
                    f"(Imbalance: {self._order_book_imbalance:.2f})"
                )

            return adjusted_bid_spread, adjusted_ask_spread

        except Exception as e:
            self.logger().error(f"Error applying order book intelligence: {str(e)}")
            return bid_spread, ask_spread

    def _adjust_for_order_book_imbalance(self, bid_spread: Decimal, ask_spread: Decimal) -> Tuple[Decimal, Decimal]:
        """Adjust spreads based on order book imbalance"""
        try:
            # Strong imbalance indicates directional pressure
            if abs(self._order_book_imbalance) > Decimal('0.3'):

                if self._order_book_imbalance > Decimal('0.3'):  # More bids (bullish)
                    # Tighten ask spread to capture upward movement
                    # Widen bid spread to avoid being picked off
                    ask_spread *= Decimal('0.9')  # 10% tighter
                    bid_spread *= Decimal('1.1')  # 10% wider

                elif self._order_book_imbalance < Decimal('-0.3'):  # More asks (bearish)
                    # Tighten bid spread to capture downward movement
                    # Widen ask spread to avoid being picked off
                    bid_spread *= Decimal('0.9')  # 10% tighter
                    ask_spread *= Decimal('1.1')  # 10% wider

                self.logger().debug(
                    f"ORDER_BOOK: Adjusted for imbalance {self._order_book_imbalance:.2f}"
                )

            return bid_spread, ask_spread

        except Exception as e:
            self.logger().error(f"Error adjusting for order book imbalance: {str(e)}")
            return bid_spread, ask_spread

    def _adjust_for_support_resistance(self, bid_spread: Decimal, ask_spread: Decimal, current_price: Decimal) -> Tuple[Decimal, Decimal]:
        """Adjust spreads based on nearby support/resistance levels"""
        try:
            if not self._support_resistance_levels:
                return bid_spread, ask_spread

            support_levels = self._support_resistance_levels.get('support', [])
            resistance_levels = self._support_resistance_levels.get('resistance', [])

            # Check for nearby support levels (within 2% of current price)
            nearby_support = [
                level for level in support_levels
                if abs(level['price'] - current_price) / current_price <= Decimal('0.02')
            ]

            # Check for nearby resistance levels (within 2% of current price)
            nearby_resistance = [
                level for level in resistance_levels
                if abs(level['price'] - current_price) / current_price <= Decimal('0.02')
            ]

            # If we're near strong support, tighten bid spreads
            if nearby_support:
                strongest_support = max(nearby_support, key=lambda x: x['strength'])
                if strongest_support['price'] < current_price:
                    bid_spread *= Decimal('0.8')  # 20% tighter near support
                    self.logger().debug(
                        f"ORDER_BOOK: Tightened bid spread near support at {strongest_support['price']:.6f}"
                    )

            # If we're near strong resistance, tighten ask spreads
            if nearby_resistance:
                strongest_resistance = max(nearby_resistance, key=lambda x: x['strength'])
                if strongest_resistance['price'] > current_price:
                    ask_spread *= Decimal('0.8')  # 20% tighter near resistance
                    self.logger().debug(
                        f"ORDER_BOOK: Tightened ask spread near resistance at {strongest_resistance['price']:.6f}"
                    )

            return bid_spread, ask_spread

        except Exception as e:
            self.logger().error(f"Error adjusting for support/resistance: {str(e)}")
            return bid_spread, ask_spread

    def _adjust_for_spread_percentiles(self, bid_spread: Decimal, ask_spread: Decimal) -> Tuple[Decimal, Decimal]:
        """Adjust spreads based on historical spread percentiles"""
        try:
            # If current spreads are much wider than typical, we can be more aggressive
            if bid_spread > self._spread_percentile_90:
                bid_spread *= Decimal('0.9')  # 10% more aggressive

            if ask_spread > self._spread_percentile_90:
                ask_spread *= Decimal('0.9')  # 10% more aggressive

            # If current spreads are much tighter than typical, be more conservative
            elif bid_spread < self._spread_percentile_10:
                bid_spread *= Decimal('1.1')  # 10% more conservative

            elif ask_spread < self._spread_percentile_10:
                ask_spread *= Decimal('1.1')  # 10% more conservative

            return bid_spread, ask_spread

        except Exception as e:
            self.logger().error(f"Error adjusting for spread percentiles: {str(e)}")
            return bid_spread, ask_spread

    def _adjust_for_market_impact(self, bid_spread: Decimal, ask_spread: Decimal) -> Tuple[Decimal, Decimal]:
        """Adjust spreads based on our estimated market impact"""
        try:
            # If our market share is high, we need wider spreads to avoid moving the market
            if self._our_market_share > Decimal('0.1'):  # > 10% market share
                impact_multiplier = Decimal('1') + self._our_market_share
                bid_spread *= impact_multiplier
                ask_spread *= impact_multiplier

                self.logger().debug(
                    f"MARKET_IMPACT: Widened spreads by {impact_multiplier:.2f}x "
                    f"due to {self._our_market_share*100:.1f}% market share"
                )

            # If we have high estimated impact, widen spreads accordingly
            if self._size_impact_curve:
                current_order_size = float(self._order_amount)
                if current_order_size in self._size_impact_curve:
                    estimated_impact = self._size_impact_curve[current_order_size]['avg_impact']

                    # If impact is high, widen spreads to compensate
                    if estimated_impact > self._max_single_order_impact * Decimal('0.5'):
                        impact_adjustment = estimated_impact / self._max_single_order_impact
                        bid_spread *= impact_adjustment
                        ask_spread *= impact_adjustment

                        self.logger().debug(
                            f"MARKET_IMPACT: Adjusted spreads by {impact_adjustment:.2f}x "
                            f"for estimated {estimated_impact*100:.2f}% impact"
                        )

            return bid_spread, ask_spread

        except Exception as e:
            self.logger().error(f"Error adjusting for market impact: {str(e)}")
            return bid_spread, ask_spread

    def _apply_dex_coordination_adjustments(self, bid_spread: Decimal, ask_spread: Decimal, current_price: Decimal) -> Tuple[Decimal, Decimal]:
        """
        Apply DEX coordination adjustments to spreads based on cross-platform analysis

        This method adjusts spreads based on:
        1. Price deviation between MEXC and DEX
        2. DEX liquidity levels
        3. Arbitrage opportunity detection
        4. Emergency stop conditions

        Args:
            bid_spread: Current bid spread
            ask_spread: Current ask spread
            current_price: Current MEXC price

        Returns:
            Tuple of adjusted (bid_spread, ask_spread)
        """
        try:
            # Safety checks to ensure all required attributes exist
            if not hasattr(self, '_cross_platform_coordinator') or not self._cross_platform_coordinator:
                return bid_spread, ask_spread

            if not hasattr(self, '_enable_dex_coordination') or not self._enable_dex_coordination:
                return bid_spread, ask_spread

            # Get market conditions from coordinator
            market_conditions = self._cross_platform_coordinator.get_market_conditions()

            # Check if emergency stop is triggered
            if market_conditions.get('emergency_stop_triggered', False):
                self.logger().warning("DEX_COORDINATION: Emergency stop triggered - widening spreads significantly")
                return bid_spread * Decimal('3.0'), ask_spread * Decimal('3.0')

            # Get base spread adjustment from coordinator
            spread_adjustment = self._cross_platform_coordinator.get_spread_adjustment()

            # Apply arbitrage protection spread adjustment
            if market_conditions.get('arbitrage_detected', False):
                arbitrage_adjustment = self._arbitrage_protection_spread_adjustment
                bid_spread += arbitrage_adjustment
                ask_spread += arbitrage_adjustment

                self.logger().info(
                    f"DEX_COORDINATION: Arbitrage detected - added {arbitrage_adjustment:.2%} to spreads"
                )

            # Apply low liquidity adjustment
            if market_conditions.get('low_liquidity_detected', False):
                liquidity_multiplier = self._low_liquidity_spread_multiplier
                bid_spread *= liquidity_multiplier
                ask_spread *= liquidity_multiplier

                dex_liquidity = market_conditions.get('dex_liquidity_usd', 0)
                self.logger().info(
                    f"DEX_COORDINATION: Low DEX liquidity (${dex_liquidity:.2f}) - "
                    f"multiplied spreads by {liquidity_multiplier:.2f}x"
                )

            # Apply price deviation based adjustments
            price_deviation = market_conditions.get('price_deviation')
            if price_deviation and price_deviation > self._max_price_deviation_pct:
                # Increase spreads proportionally to price deviation
                deviation_multiplier = Decimal('1') + (price_deviation - self._max_price_deviation_pct) * 2
                bid_spread *= deviation_multiplier
                ask_spread *= deviation_multiplier

                mexc_price = market_conditions.get('mexc_price', current_price)
                dex_price = market_conditions.get('dex_price', current_price)

                self.logger().warning(
                    f"DEX_COORDINATION: High price deviation ({price_deviation:.2%}) - "
                    f"MEXC: ${mexc_price:.6f}, DEX: ${dex_price:.6f}, "
                    f"multiplied spreads by {deviation_multiplier:.2f}x"
                )

            # Ensure spreads don't go below minimum
            bid_spread = max(bid_spread, self._min_allowed_spread)
            ask_spread = max(ask_spread, self._min_allowed_spread)

            # Log periodic DEX coordination status
            current_time = time.time()
            if not hasattr(self, '_last_dex_coordination_log'):
                self._last_dex_coordination_log = 0

            if current_time - self._last_dex_coordination_log > 300:  # Every 5 minutes
                self._log_dex_coordination_status(market_conditions, bid_spread, ask_spread)
                self._last_dex_coordination_log = current_time

            return bid_spread, ask_spread

        except Exception as e:
            self.logger().error(f"DEX_COORDINATION: Error applying coordination adjustments: {str(e)}")
            return bid_spread, ask_spread

    def _log_dex_coordination_status(self, market_conditions: Dict, bid_spread: Decimal, ask_spread: Decimal):
        """Log comprehensive DEX coordination status"""
        try:
            mexc_price = market_conditions.get('mexc_price', 0)
            dex_price = market_conditions.get('dex_price', 0)
            price_deviation = market_conditions.get('price_deviation', 0)
            dex_liquidity = market_conditions.get('dex_liquidity_usd', 0)
            dex_volume = market_conditions.get('dex_volume_24h_usd', 0)
            arbitrage_detected = market_conditions.get('arbitrage_detected', False)
            low_liquidity = market_conditions.get('low_liquidity_detected', False)
            emergency_stop = market_conditions.get('emergency_stop_triggered', False)

            self.logger().info("=" * 60)
            self.logger().info("🔗 DEX COORDINATION STATUS")
            self.logger().info("=" * 60)
            self.logger().info(f"💱 MEXC Price: ${mexc_price:.6f}")
            self.logger().info(f"🔗 DEX Price: ${dex_price:.6f}")
            self.logger().info(f"📈 Price Deviation: {price_deviation:.2%}")
            self.logger().info(f"💧 DEX Liquidity: ${dex_liquidity:.2f}")
            self.logger().info(f"📊 DEX 24h Volume: ${dex_volume:.2f}")
            self.logger().info(f"📏 Current Bid Spread: {bid_spread:.2%}")
            self.logger().info(f"📏 Current Ask Spread: {ask_spread:.2%}")
            self.logger().info(f"⚠️  Arbitrage Detected: {arbitrage_detected}")
            self.logger().info(f"🔻 Low Liquidity: {low_liquidity}")
            self.logger().info(f"🛑 Emergency Stop: {emergency_stop}")
            self.logger().info("=" * 60)

        except Exception as e:
            self.logger().error(f"DEX_COORDINATION: Error logging status: {str(e)}")

    def _get_avg_buy_sell_ratio(self) -> float:
        """
        Calculate the ratio of average buy price to average sell price.

        Returns:
            float: The ratio of average buy price to average sell price.
                  Returns 1.0 if there's insufficient data or an error occurs.
        """
        try:
            # If we have no trades yet, return the default ratio of 1.0
            if not self._buy_prices or not self._sell_prices:
                return 1.0

            # Minimum number of trades required before activating protection
            min_required_trades = 15

            # Check if we have enough trades to consider the ratio reliable
            if len(self._buy_prices) < min_required_trades or len(self._sell_prices) < min_required_trades:
                if self._logging_options & self.OPTION_LOG_MAKER_ORDER_FILLED:
                    self.logger().debug(
                        f"Insufficient trades for ratio protection. "
                        f"Buy trades: {len(self._buy_prices)}, Sell trades: {len(self._sell_prices)} "
                        f"(min required: {min_required_trades})"
                    )
                return 1.0

            # Calculate averages with outlier removal, using only the last 15 trades
            def get_robust_average(prices, max_trades=15):
                if not prices:
                    return Decimal('0')
                # Get the last max_trades prices (or fewer if not enough data)
                last_prices = list(prices)[-max_trades:]
                if not last_prices:
                    return Decimal('0')

                prices_sorted = sorted(last_prices)
                # Remove top and bottom 10% to reduce impact of outliers
                remove_count = max(1, int(len(prices_sorted) * 0.1))
                clean_prices = prices_sorted[remove_count:-remove_count] if len(prices_sorted) > 2 * remove_count else prices_sorted
                return sum(clean_prices) / len(clean_prices) if clean_prices else Decimal('0')

            # Use only the last 15 trades for the ratio calculation
            avg_buy = get_robust_average(self._buy_prices, 15)
            avg_sell = get_robust_average(self._sell_prices, 15)

            if avg_sell <= 0:
                if self._logging_options & self.OPTION_LOG_MAKER_ORDER_FILLED:
                    self.logger().debug(f"Invalid average sell price: {avg_sell}")
                return 1.0

            ratio = float(avg_buy / avg_sell)

            # Log the current ratio periodically based on log level
            now = time.time()
            if now - self._last_ratio_warning > self._ratio_warning_interval:
                log_message = (
                    f"Buy/Sell Price Analysis - "
                    f"Ratio: {ratio:.4f}, "
                    f"Avg Buy: {avg_buy:.8f}, "
                    f"Avg Sell: {avg_sell:.8f}, "
                    f"Samples: {len(self._buy_prices)} buys, {len(self._sell_prices)} sells"
                )

                # Log based on configured log level and ratio threshold
                if self._price_ratio_log_level == "INFO" or \
                   (self._price_ratio_log_level == "WARNING" and ratio > float(self._max_buy_sell_ratio)) or \
                   (self._price_ratio_log_level == "ERROR" and ratio > float(self._max_buy_sell_ratio) * 1.02):

                    if self._price_ratio_log_level == "INFO":
                        self.logger().info(log_message)
                    elif self._price_ratio_log_level == "WARNING":
                        self.logger().warning(log_message)
                    else:  # ERROR
                        self.logger().error(log_message)

                    self._last_ratio_warning = now

            return ratio

        except Exception as e:
            self.logger().error(f"Error calculating buy/sell ratio: {str(e)}", exc_info=True)
            return 1.0

    def _adjust_spreads_for_ratio(self, ratio: float) -> Tuple[Decimal, Decimal]:
        """
        Adjust bid/ask spreads based on the buy/sell price ratio.

        This method calculates new spreads based on how much the current ratio exceeds the maximum
        allowed ratio. The spread increase is proportional to how much the ratio exceeds the threshold.

        Args:
            ratio: The current ratio of average buy price to average sell price

        Returns:
            Tuple[Decimal, Decimal]: The adjusted (bid_spread, ask_spread) values
        """
        try:
            if not self._enable_spread_adjustment:
                return self._original_bid_spread, self._original_ask_spread

            # Initialize with original spreads
            bid_spread = self._original_bid_spread
            ask_spread = self._original_ask_spread

            # Check if we have enough trades to consider the ratio reliable
            min_required_trades = 15
            if len(self._buy_prices) < min_required_trades or len(self._sell_prices) < min_required_trades:
                return bid_spread, ask_spread

            # Calculate how much we need to adjust the spreads
            max_ratio = float(self._max_buy_sell_ratio)

            # Only adjust if we're above the maximum allowed ratio
            if ratio > max_ratio:
                # Calculate how much we're over the threshold (0.0 to 1.0)
                ratio_excess = min((ratio - max_ratio) / (1.0 - max_ratio), 1.0)

                # Calculate spread increase based on how much we're over the threshold
                spread_increase = float(self._spread_adjustment_increment) * ratio_excess

                # Apply the increase to both spreads
                bid_spread = self._original_bid_spread + Decimal(str(spread_increase))
                ask_spread = self._original_ask_spread + Decimal(str(spread_increase))

                # Ensure we don't go below minimum allowed spread
                bid_spread = max(bid_spread, self._min_allowed_spread)
                ask_spread = max(ask_spread, self._min_allowed_spread)

                # Log the adjustment if it's significant
                if spread_increase > 0.0001:  # Only log if the increase is more than 0.01%
                    self.logger().info(
                        f"Price ratio {ratio:.4f} exceeds threshold {max_ratio:.4f}. "
                        f"Increasing spreads by +{spread_increase*100:.2f}% to "
                        f"bid={float(bid_spread)*100:.2f}%, ask={float(ask_spread)*100:.2f}%"
                    )

            return bid_spread, ask_spread

        except Exception as e:
            self.logger().error(f"Error adjusting spreads: {str(e)}", exc_info=True)
            # Return original spreads in case of error
            return self._original_bid_spread, self._original_ask_spread

    cdef object c_create_base_proposal(self):
        cdef:
            ExchangeBase market = self._market_info.market
            list buys = []
            list sells = []
            object current_price = self.get_price()
            bint is_above_upper = current_price >= self._upper_threshold
            bint is_below_lower = current_price <= self._lower_threshold
            bint is_bot_attack = self._is_bot_attack(current_price)

        # PRODUCTION SAFETY: Analyze price trends and update trading boundaries
        # This MUST be called before creating any orders to prevent systematic losses
        self._analyze_price_trend_and_update_boundaries(current_price)

        # Update average prices for filled orders
        for order in self.active_orders:
            if order.is_filled:
                self._update_avg_prices(order.is_buy, order.price)

        # Check and adjust strategy based on average prices
        avg_ratio = self._get_avg_buy_sell_ratio()

        # Adjust spreads based on buy/sell price ratio
        current_bid_spread, current_ask_spread = self._adjust_spreads_for_ratio(avg_ratio)

        # ADVERSE SELECTION PROTECTION: Apply volatility and momentum-based spread adjustments
        current_bid_spread, current_ask_spread = self._apply_adverse_selection_spread_adjustments(
            current_bid_spread, current_ask_spread
        )

        # ORDER BOOK INTELLIGENCE & MARKET IMPACT: Apply intelligent positioning and sizing
        current_bid_spread, current_ask_spread = self._apply_orderbook_intelligence(
            current_bid_spread, current_ask_spread, current_price
        )

        # DEX COORDINATION: Apply cross-platform spread adjustments
        if (hasattr(self, '_enable_dex_coordination') and self._enable_dex_coordination and
            hasattr(self, '_cross_platform_coordinator') and self._cross_platform_coordinator):
            current_bid_spread, current_ask_spread = self._apply_dex_coordination_adjustments(
                current_bid_spread, current_ask_spread, current_price
            )

        # Initialize adjusted order amount with the original value
        adjusted_order_amount = self._order_amount

        # MARKET IMPACT: Apply optimal order sizing
        if hasattr(self, '_optimal_order_size') and self._optimal_order_size > 0:
            adjusted_order_amount = self._optimal_order_size

            # Log significant size changes
            size_ratio = adjusted_order_amount / self._order_amount
            if abs(size_ratio - Decimal('1.0')) > Decimal('0.2'):  # >20% change
                self.logger().debug(
                    f"MARKET_IMPACT: Using optimal order size {adjusted_order_amount:.2f} "
                    f"({size_ratio:.1f}x base size)"
                )

        # If ratio is too high, consider more aggressive actions
        min_required_trades = 15
        if (self._enable_spread_adjustment and
            avg_ratio > float(self._max_buy_sell_ratio) and
            len(self._buy_prices) >= min_required_trades and
            len(self._sell_prices) >= min_required_trades):

            # Calculate reduction factor (up to 50% reduction)
            ratio_excess = (avg_ratio - float(self._max_buy_sell_ratio)) / (1.0 - float(self._max_buy_sell_ratio))
            reduction_factor = max(0.5, 1.0 - ratio_excess * 0.5)  # Up to 50% reduction
            adjusted_order_amount = self._order_amount * Decimal(str(reduction_factor))

            # Log the order size reduction
            if self._hb_app_notification and reduction_factor < 1.0:
                self.logger().warning(
                    f"High buy/sell price ratio ({avg_ratio:.4f} > {float(self._max_buy_sell_ratio):.4f}). "
                    f"Reducing order size by {int((1 - reduction_factor) * 100)}% to {adjusted_order_amount}"
                )

                # Log the current state for debugging
                if self._buy_prices and self._sell_prices:
                    avg_buy = sum(self._buy_prices) / len(self._buy_prices)
                    avg_sell = sum(self._sell_prices) / len(self._sell_prices)
                    self.logger().debug(
                        f"Price ratio monitoring - "
                        f"Avg Buy: {avg_buy:.8f}, Avg Sell: {avg_sell:.8f}, "
                        f"Ratio: {avg_ratio:.4f}, "
                        f"Original Spreads: bid={self._original_bid_spread*100:.2f}%, ask={self._original_ask_spread*100:.2f}%, "
                        f"Adjusted Spreads: bid={current_bid_spread*100:.2f}%, ask={current_ask_spread*100:.2f}%"
                    )

        # Apply price thresholds and bot attack logic
        if is_bot_attack:
            # In attack mode, be more conservative with orders
            if current_price > self._attack_start_price:
                # If price went up during attack, only place sell orders
                sell_price = self._round_to_significant(current_price * (1 + current_ask_spread / 200))  # Use adjusted spread
                sells = [PriceSize(sell_price, adjusted_order_amount)]
                self.logger().warning(f"Bot attack detected! Only placing sell orders at {sell_price}")
            else:
                # If price is dropping, only place buy orders below attack start
                buy_price = self._round_to_significant(min(current_price * Decimal('0.995'),
                                                         self._attack_start_price * Decimal('0.99')))  # 0.5-1% below
                buys = [PriceSize(buy_price, adjusted_order_amount)]
                self.logger().warning(f"Bot attack recovery! Only placing buy orders at {buy_price}")
        elif self._price_ceiling > 0 and current_price >= self._price_ceiling:
            # At or above price ceiling, only place sell orders
            sell_price = self._round_to_significant(current_price * (1 + current_ask_spread / 200))
            sells = [PriceSize(sell_price, adjusted_order_amount)]
            self.logger().info(f"Price at or above ceiling {self._price_ceiling}, only placing sell orders at {sell_price}")
        elif self._price_floor > 0 and current_price <= self._price_floor:
            # At or below price floor, only place buy orders
            buy_price = self._round_to_significant(current_price * (1 - current_bid_spread / 200))
            buys = [PriceSize(buy_price, adjusted_order_amount)]
            self.logger().info(f"Price at or below floor {self._price_floor}, only placing buy orders at {buy_price}")
        else:
            # Normal operation between thresholds
            # ENHANCED ANTI-ARBITRAGE STRATEGY: Use different reference prices for buy vs sell orders
            # to minimize arbitrage opportunities and protect funds
            buy_reference_price = sell_reference_price = current_price

            # Check if DEX coordination is enabled and get separate MEXC/DEX prices
            # IMPORTANT: Only run anti-arbitrage logic if market is ready and has order book
            market_ready = True
            try:
                # Test if we can get market price without crashing
                test_price = self._market_info.get_mid_price()
                if test_price.is_nan() or test_price <= 0:
                    market_ready = False
            except:
                market_ready = False

            if not market_ready:
                self.logger().debug("ANTI-ARBITRAGE: Market not ready, skipping anti-arbitrage logic")
                buy_reference_price = sell_reference_price = current_price
            else:
                # ANTI-ARBITRAGE STRATEGY: Check for DEX price delegate availability
                # This approach works even when the coordinator fails due to MEXC order book issues
                has_dex_delegate = hasattr(self, '_dex_price_delegate')
                dex_delegate_exists = has_dex_delegate and self._dex_price_delegate
                dex_delegate_ready = dex_delegate_exists and self._dex_price_delegate.ready

                # WORKAROUND: Try direct access to bypass hasattr issue
                try:
                    direct_delegate = self._dex_price_delegate
                    direct_delegate_exists = direct_delegate is not None
                    direct_delegate_ready = direct_delegate_exists and direct_delegate.ready
                except AttributeError:
                    direct_delegate = None
                    direct_delegate_exists = False
                    direct_delegate_ready = False

                # DEBUG: Log detailed DEX delegate status
                self.logger().info(f"ANTI-ARBITRAGE DEBUG: has_dex_delegate={has_dex_delegate}, dex_delegate_exists={dex_delegate_exists}, dex_delegate_ready={dex_delegate_ready}")
                self.logger().info(f"ANTI-ARBITRAGE DEBUG: DIRECT: direct_delegate_exists={direct_delegate_exists}, direct_delegate_ready={direct_delegate_ready}")
                self.logger().info(f"ANTI-ARBITRAGE DEBUG: All attributes: {[attr for attr in dir(self) if 'dex' in attr.lower()]}")
                if dex_delegate_exists:
                    self.logger().info(f"ANTI-ARBITRAGE DEBUG: DEX delegate _is_ready={self._dex_price_delegate._is_ready}, _current_price={self._dex_price_delegate._current_price}, staleness_check={time.time() - self._dex_price_delegate._last_update_time < self._dex_price_delegate._staleness_threshold}")
                elif direct_delegate_exists:
                    self.logger().info(f"ANTI-ARBITRAGE DEBUG: DIRECT DEX delegate _is_ready={direct_delegate._is_ready}, _current_price={direct_delegate._current_price}, staleness_check={time.time() - direct_delegate._last_update_time < direct_delegate._staleness_threshold}")
                else:
                    self.logger().info(f"ANTI-ARBITRAGE DEBUG: DEX delegate does not exist. _dex_price_delegate value: {getattr(self, '_dex_price_delegate', 'ATTRIBUTE_NOT_FOUND')}")

                # Try coordinator first, then fallback to direct DEX delegate
                has_coordinator = hasattr(self, '_cross_platform_coordinator')
                coordinator_exists = has_coordinator and self._cross_platform_coordinator

                self.logger().info(f"ANTI-ARBITRAGE DEBUG: has_coordinator={has_coordinator}, coordinator_exists={coordinator_exists}")

                if coordinator_exists:
                    try:
                        # Try to get prices from the coordinator
                        mexc_price = self._market_info.get_mid_price()
                        # Check if coordinator has DEX price information
                        if hasattr(self._cross_platform_coordinator, '_dex_price_delegate') and self._cross_platform_coordinator._dex_price_delegate:
                            dex_price = self._cross_platform_coordinator._dex_price_delegate.c_get_mid_price()
                            self.logger().info(f"ANTI-ARBITRAGE DEBUG: Got prices via coordinator - MEXC: ${mexc_price:.8f}, DEX: ${dex_price:.8f}")

                            if not dex_price.is_nan() and dex_price > 0 and not mexc_price.is_nan() and mexc_price > 0:
                                price_gap_pct = abs(mexc_price - dex_price) / min(mexc_price, dex_price) * 100

                                # Only apply anti-arbitrage logic if gap is significant (>0.1%)
                                if price_gap_pct > 0.0: # TESTING: Trigger on any price gap (>0.0%)
                                    # Check arbitrage defense mode
                                    if self._arbitrage_defense_mode == "max_price":
                                        # MAX_PRICE MODE: Always use higher price for both buy and sell
                                        buy_reference_price = sell_reference_price = max(mexc_price, dex_price)
                                        self.logger().info(
                                            f"ANTI-ARBITRAGE [MAX_PRICE]: Using higher price ${buy_reference_price:.8f} for both buy/sell, "
                                            f"MEXC: ${mexc_price:.8f}, DEX: ${dex_price:.8f}, gap: {price_gap_pct:.2f}%"
                                        )
                                    else:
                                        # NORMAL MODE: Use optimal arbitrage defense strategy
                                        if dex_price < mexc_price:
                                            # DEX price is lower than MEXC price
                                            # Buy orders: use lower DEX price to buy cheaper
                                            # Sell orders: use higher MEXC price to sell higher
                                            buy_reference_price = dex_price
                                            sell_reference_price = mexc_price
                                            self.logger().info(
                                                f"ANTI-ARBITRAGE [NORMAL]: DEX lower (${dex_price:.8f}) than MEXC (${mexc_price:.8f}), "
                                                f"gap: {price_gap_pct:.2f}% - Buy ref: DEX, Sell ref: MEXC"
                                            )
                                        else:
                                            # MEXC price is lower than or equal to DEX price
                                            # Use higher DEX price for both buy and sell to support token price
                                            buy_reference_price = sell_reference_price = dex_price
                                            self.logger().info(
                                                f"ANTI-ARBITRAGE [NORMAL]: MEXC lower/equal (${mexc_price:.8f}) than DEX (${dex_price:.8f}), "
                                                f"gap: {price_gap_pct:.2f}% - Both ref: DEX (higher price support)"
                                            )
                                else:
                                    # Small gap - use current price (which is already adjusted by get_price())
                                    buy_reference_price = sell_reference_price = current_price
                                    self.logger().debug(
                                        f"ANTI-ARBITRAGE: Small gap {price_gap_pct:.2f}% (<0.1%), using adjusted current price ${current_price:.8f}"
                                    )

                                # Log the price comparison for debugging
                                self.logger().info(
                                    f"ANTI-ARBITRAGE: Price comparison - MEXC: ${mexc_price:.8f}, DEX: ${dex_price:.8f}, "
                                    f"Gap: {price_gap_pct:.2f}%, Current (adjusted): ${current_price:.8f}"
                                )
                            else:
                                self.logger().warning("ANTI-ARBITRAGE: Invalid DEX or MEXC price via coordinator, using current price")
                                buy_reference_price = sell_reference_price = current_price
                    except Exception as e:
                        self.logger().warning(f"ANTI-ARBITRAGE: Error getting prices via coordinator: {str(e)}, using current price")
                        buy_reference_price = sell_reference_price = current_price
                elif (dex_delegate_exists and dex_delegate_ready) or (direct_delegate_exists and direct_delegate_ready):
                    # FALLBACK: Use DEX delegate directly when coordinator is not available
                    try:
                        # Get raw MEXC price directly from market
                        mexc_price = self._market_info.get_mid_price()
                        # Use direct delegate if available, otherwise use hasattr approach
                        if direct_delegate_exists:
                            dex_price = direct_delegate.c_get_mid_price()
                        else:
                            dex_price = self._dex_price_delegate.c_get_mid_price()

                        if not dex_price.is_nan() and dex_price > 0 and not mexc_price.is_nan() and mexc_price > 0:
                            price_gap_pct = abs(mexc_price - dex_price) / min(mexc_price, dex_price) * 100

                            # ENHANCED ANTI-ARBITRAGE STRATEGY: Use different reference prices for buy vs sell orders
                            # to minimize arbitrage opportunities and protect funds
                            if price_gap_pct > 0.0:  # TESTING: Trigger on any price gap (>0.0%)
                                # Check arbitrage defense mode
                                if self._arbitrage_defense_mode == "max_price":
                                    # MAX_PRICE MODE: Always use higher price for both buy and sell
                                    buy_reference_price = sell_reference_price = max(mexc_price, dex_price)
                                    self.logger().info(f"ANTI-ARBITRAGE [MAX_PRICE]: Using higher price ${buy_reference_price:.8f} for both buy/sell, Gap: {price_gap_pct:.2f}%")
                                else:
                                    # NORMAL MODE: Use optimal arbitrage defense strategy
                                    if dex_price < mexc_price:
                                        # DEX price is lower than MEXC - use DEX price for buy orders, MEXC for sell orders
                                        buy_reference_price = dex_price
                                        sell_reference_price = mexc_price
                                        self.logger().info(f"ANTI-ARBITRAGE [NORMAL]: DEX price lower - Buy ref: ${buy_reference_price:.8f} (DEX), Sell ref: ${sell_reference_price:.8f} (MEXC), Gap: {price_gap_pct:.2f}%")
                                    else:
                                        # DEX price is higher than MEXC - use higher price for both to minimize arbitrage gap
                                        buy_reference_price = sell_reference_price = max(mexc_price, dex_price)
                                        self.logger().info(f"ANTI-ARBITRAGE [NORMAL]: DEX price higher - Using higher price ${buy_reference_price:.8f} for both buy and sell, Gap: {price_gap_pct:.2f}%")
                            else:
                                # Price gap is small, use current price
                                buy_reference_price = sell_reference_price = current_price
                                self.logger().debug(f"ANTI-ARBITRAGE: Small price gap ({price_gap_pct:.2f}%), using current price")

                            # Log the price comparison for debugging
                            self.logger().info(
                                f"ANTI-ARBITRAGE: Direct DEX mode - MEXC: ${mexc_price:.8f}, DEX: ${dex_price:.8f}, "
                                f"Gap: {price_gap_pct:.2f}%, Buy ref: ${buy_reference_price:.8f}, Sell ref: ${sell_reference_price:.8f}"
                            )
                        else:
                            self.logger().warning("ANTI-ARBITRAGE: Invalid DEX or MEXC price, using current price")
                            buy_reference_price = sell_reference_price = current_price

                    except Exception as e:
                        self.logger().warning(f"ANTI-ARBITRAGE: Error getting prices via DEX delegate: {str(e)}, using current price")
                        buy_reference_price = sell_reference_price = current_price
                else:
                    # No DEX coordination available, use current price
                    self.logger().debug("ANTI-ARBITRAGE: No DEX coordination available, using current price")
                    buy_reference_price = sell_reference_price = current_price

            # Use adjusted spreads for normal operation
            self._bid_spread = current_bid_spread
            self._ask_spread = current_ask_spread

            # Apply price type overrides (these will override the anti-arbitrage logic if specified)
            if self._price_type is PriceType.InventoryCost and self._inventory_cost_price_delegate is not None:
                buy_reference_price = sell_reference_price = self._inventory_cost_price_delegate.get_price()
            elif self._price_type is PriceType.LastOwnTrade and not self._last_own_trade_price.is_nan():
                buy_reference_price = sell_reference_price = self._last_own_trade_price
            elif self._price_type is PriceType.Custom and self._asset_price_delegate is not None:
                buy_reference_price = sell_reference_price = self._asset_price_delegate.c_get_mid_price()

            if self._price_type is PriceType.BestBid:
                buy_reference_price = self._market_info.get_price(True)
            elif self._price_type is PriceType.BestAsk:
                sell_reference_price = self._market_info.get_price(False)

            # Apply inventory skew with clean price levels
            if self._inventory_skew_enabled:
                base_balance = self._market_info.get_balance(self._market_info.base_asset)
                quote_balance = self._market_info.get_balance(self._market_info.quote_asset)
                total_value = base_balance * current_price + quote_balance
                target_base_value = total_value * (self._inventory_target_base_pct / 100)
                current_base_value = base_balance * current_price

                if current_base_value < target_base_value * (1 - self._inventory_range_multiplier * self._order_amount):
                    # Need to buy more base
                    buy_price = self._round_to_significant(buy_reference_price * (1 - self._bid_spread / 100))
                    buys = [PriceSize(buy_price, self._order_amount)]
                elif current_base_value > target_base_value * (1 + self._inventory_range_multiplier * self._order_amount):
                    # Need to sell base
                    sell_price = self._round_to_significant(sell_reference_price * (1 + self._ask_spread / 100))
                    sells = [PriceSize(sell_price, self._order_amount)]
                else:
                    # Within target range, create normal orders with clean levels
                    buy_price = self._round_to_significant(buy_reference_price * (1 - self._bid_spread / 100))
                    sell_price = self._round_to_significant(sell_reference_price * (1 + self._ask_spread / 100))

                    # SAFETY CHECK: Ensure prices are on correct side of market
                    if sell_price <= current_price:
                        corrected_sell_price = self._round_to_significant(current_price * (1 + self._ask_spread / 100))
                        self.logger().error(f"INVENTORY SKEW: Correcting sell price from {sell_price:.8f} to {corrected_sell_price:.8f}")
                        sell_price = corrected_sell_price
                    if buy_price >= current_price:
                        corrected_buy_price = self._round_to_significant(current_price * (1 - self._bid_spread / 100))
                        self.logger().error(f"INVENTORY SKEW: Correcting buy price from {buy_price:.8f} to {corrected_buy_price:.8f}")
                        buy_price = corrected_buy_price

                    buys = [PriceSize(buy_price, self._order_amount)]
                    sells = [PriceSize(sell_price, self._order_amount)]
            else:
                # No inventory skew, create normal orders with clean levels
                # DEBUG: Calculate prices step by step to identify the issue
                raw_buy_price = buy_reference_price * (1 - self._bid_spread / 100)
                raw_sell_price = sell_reference_price * (1 + self._ask_spread / 100)
                buy_price = self._round_to_significant(raw_buy_price)
                sell_price = self._round_to_significant(raw_sell_price)

                # SAFETY DEBUG: Log reference prices and calculated order prices
                # Show if we're using different reference prices for anti-arbitrage protection
                ref_price_info = ""
                if buy_reference_price != sell_reference_price:
                    ref_price_info = " [ANTI-ARBITRAGE: Different buy/sell refs]"
                elif buy_reference_price != current_price:
                    ref_price_info = " [PRICE_TYPE: Custom reference]"

                self.logger().info(
                    f"ORDER_CREATION: Current price: {current_price:.8f}, "
                    f"Buy ref: {buy_reference_price:.8f}, Sell ref: {sell_reference_price:.8f}{ref_price_info}, "
                    f"Bid spread: {self._bid_spread}%, Ask spread: {self._ask_spread}%, "
                    f"Raw buy: {raw_buy_price:.8f}, Raw sell: {raw_sell_price:.8f}, "
                    f"Rounded buy: {buy_price:.8f}, Rounded sell: {sell_price:.8f}"
                )

                # SAFETY CHECK: Ensure sell price is above current price and buy price is below
                if sell_price <= current_price:
                    self.logger().error(
                        f"CRITICAL ERROR: Sell price {sell_price:.8f} is not above current price {current_price:.8f}! "
                        f"This indicates a serious issue with price calculation. "
                        f"Sell reference: {sell_reference_price:.8f}, Ask spread: {self._ask_spread}%"
                    )
                    # Fix by using current price as reference with correct spread calculation
                    corrected_sell_price = self._round_to_significant(current_price * (1 + self._ask_spread / 100))
                    self.logger().warning(f"SAFETY FIX: Corrected sell price from {sell_price:.8f} to {corrected_sell_price:.8f}")
                    sell_price = corrected_sell_price

                if buy_price >= current_price:
                    self.logger().error(
                        f"CRITICAL ERROR: Buy price {buy_price:.8f} is not below current price {current_price:.8f}! "
                        f"This indicates a serious issue with price calculation. "
                        f"Buy reference: {buy_reference_price:.8f}, Bid spread: {self._bid_spread}%"
                    )
                    # Fix by using current price as reference with correct spread calculation
                    corrected_buy_price = self._round_to_significant(current_price * (1 - self._bid_spread / 100))
                    self.logger().warning(f"SAFETY FIX: Corrected buy price from {buy_price:.8f} to {corrected_buy_price:.8f}")
                    buy_price = corrected_buy_price

                buys = [PriceSize(buy_price, self._order_amount)]
                sells = [PriceSize(sell_price, self._order_amount)]

            # Add multiple order levels if enabled
            if self._order_levels > 1:
                for i in range(1, self._order_levels):
                    level_multiplier = Decimal(str(i)) * self._order_level_spread / 100
                    level_amount = self._order_amount * (self._order_level_amount ** i)

                    # Add buy levels with clean prices
                    if buys:
                        level_buy_price = self._round_to_significant(buy_reference_price * (1 - (self._bid_spread / 100 + level_multiplier)))
                        buys.append(PriceSize(level_buy_price, level_amount))

                    # Add sell levels with clean prices
                    if sells:
                        level_sell_price = self._round_to_significant(sell_reference_price * (1 + (self._ask_spread / 100 + level_multiplier)))
                        sells.append(PriceSize(level_sell_price, level_amount))

        # ANTI-DRAIN PROTECTION: Apply sophisticated bot attack protection for own-token market making
        if self._enable_anti_drain_protection:
            buys, sells = self._apply_anti_drain_protection(buys, sells, current_price)

        # OWN-TOKEN SAFETY: Apply own-token market making safety checks
        if hasattr(self, '_enable_own_token_safety') and self._enable_own_token_safety:
            # Check overall safety limits first
            if not self._check_own_token_safety(current_price):
                self.logger().error("OWN_TOKEN_SAFETY: Emergency stop active - no orders will be placed")
                return Proposal([], [])

            # Validate individual orders
            safe_buys = []
            safe_sells = []

            for buy in buys:
                is_safe, reason = self._validate_own_token_order(True, buy.price, buy.size, current_price)
                if is_safe:
                    safe_buys.append(buy)
                    self._update_own_token_tracking(True, buy.price, buy.size)
                else:
                    self.logger().warning(f"OWN_TOKEN_SAFETY: Rejected buy order - {reason}")

            for sell in sells:
                is_safe, reason = self._validate_own_token_order(False, sell.price, sell.size, current_price)
                if is_safe:
                    safe_sells.append(sell)
                    self._update_own_token_tracking(False, sell.price, sell.size)
                else:
                    self.logger().warning(f"OWN_TOKEN_SAFETY: Rejected sell order - {reason}")

            buys = safe_buys
            sells = safe_sells

            self.logger().info(
                f"OWN_TOKEN_SAFETY: Validated orders - {len(buys)} buys, {len(sells)} sells. "
                f"Daily P&L: ${self._daily_pnl_usd:.2f}, Safety violations: {self._safety_violations}"
            )

        # PRODUCTION SAFETY: Apply buy-low/sell-high validation to prevent systematic losses
        # This is the CRITICAL safety check that prevents unprofitable trades
        # DISABLED for own-token market making when DEX coordination is enabled
        elif self._enforce_buy_low_sell_high and not (hasattr(self, '_enable_dex_coordination') and self._enable_dex_coordination):
            self.logger().info(f"SAFETY: Applying safety checks (DEX coordination: {getattr(self, '_enable_dex_coordination', 'NOT_SET')})")
            safe_buys = []
            safe_sells = []

            # Validate each buy order
            for buy in buys:
                is_safe, reason = self._validate_buy_order_safety(buy.price, current_price)
                if is_safe:
                    safe_buys.append(buy)
                else:
                    self._log_order_rejection("BUY", buy.price, reason)
                    self.logger().warning(f"SAFETY: Rejected BUY order at {buy.price:.8f} - {reason}")

            # Validate each sell order
            for sell in sells:
                is_safe, reason = self._validate_sell_order_safety(sell.price, current_price)
                if is_safe:
                    safe_sells.append(sell)
                else:
                    self._log_order_rejection("SELL", sell.price, reason)
                    self.logger().warning(f"SAFETY: Rejected SELL order at {sell.price:.8f} - {reason}")

            # Log safety filter results
            if len(safe_buys) != len(buys) or len(safe_sells) != len(sells):
                self.logger().warning(
                    f"SAFETY: Order safety filter applied - "
                    f"Buy orders: {len(buys)} -> {len(safe_buys)}, "
                    f"Sell orders: {len(sells)} -> {len(safe_sells)}. "
                    f"Trend: {self._current_price_trend}, "
                    f"Safe buy ceiling: {self._safe_buy_ceiling:.8f}, "
                    f"Safe sell floor: {self._safe_sell_floor:.8f}"
                )
            else:
                self.logger().debug(
                    f"SAFETY: All orders passed safety validation - "
                    f"Buy orders: {len(safe_buys)}, Sell orders: {len(safe_sells)}"
                )

            buys = safe_buys
            sells = safe_sells
        else:
            # Safety system disabled - check why and log appropriately
            if hasattr(self, '_enable_dex_coordination') and self._enable_dex_coordination:
                if hasattr(self, '_enable_own_token_safety') and self._enable_own_token_safety:
                    self.logger().info(
                        f"SAFETY: Using OWN-TOKEN safety mode with daily stop loss and 8-hour monitoring. "
                        f"Traditional buy-low/sell-high enforcement bypassed for price support."
                    )
                else:
                    self.logger().warning(
                        f"SAFETY: Buy-low/sell-high enforcement BYPASSED for own-token market making "
                        f"(DEX coordination enabled). Orders will be placed without profit validation."
                    )
            else:
                self.logger().warning(
                    f"SAFETY: Buy-low/sell-high enforcement is DISABLED! "
                    f"This should only be used for testing. "
                    f"For production with significant capital, enable enforce_buy_low_sell_high=True"
                )

        return Proposal(buys, sells)

    def _apply_anti_drain_protection(self, buys: List[PriceSize], sells: List[PriceSize], current_price: Decimal) -> Tuple[List[PriceSize], List[PriceSize]]:
        """
        ANTI-DRAIN PROTECTION: Sophisticated protection against bot attacks for own-token market making.

        Protects against:
        1. Volume-based attacks (high volume in short time)
        2. Imbalanced trading (only buying or only selling)
        3. Rapid-fire trading patterns
        4. Price manipulation attempts

        Args:
            buys: List of proposed buy orders
            sells: List of proposed sell orders
            current_price: Current market price

        Returns:
            Tuple of filtered (buys, sells) lists
        """
        now = time.time()

        # Update volume tracking
        self._update_volume_tracking(now)

        # Handle graduated alert levels with appropriate responses
        if self._alert_level == "RED" or (self._attack_detected and (now - self._attack_start_time) < self._attack_cooldown):
            remaining_cooldown = self._attack_cooldown - (now - self._attack_start_time) if self._attack_detected else 0

            # Handle different attack phases with specific strategies
            if self._attack_phase == "EMERGENCY":
                self.logger().error(
                    f"ANTI-DRAIN: EMERGENCY MODE ACTIVE! Balance drain exceeded limits. "
                    f"NO ORDERS until manual intervention."
                )
                return [], []  # Complete stop during emergency

            elif self._attack_phase == "DUMP":
                self.logger().warning(
                    f"ANTI-DRAIN: DUMP PHASE ACTIVE ({remaining_cooldown:.1f}s remaining). "
                    f"REFUSING TO BUY - this is the liquidity drain attempt!"
                )
                return [], []  # NO ORDERS during dump phase - don't feed the attacker

            elif self._attack_phase == "PUMP":
                self.logger().warning(
                    f"ANTI-DRAIN: PUMP PHASE ACTIVE ({remaining_cooldown:.1f}s remaining). "
                    f"Raising liquidity and limiting buys to attack start price level."
                )
                return self._create_pump_defense_orders(buys, current_price), self._create_pump_phase_sells(sells, current_price)

            elif self._attack_phase == "COORDINATED":
                self.logger().warning(
                    f"ANTI-DRAIN: COORDINATED ATTACK ACTIVE ({remaining_cooldown:.1f}s remaining). "
                    f"Minimal order creation only."
                )
                return self._create_minimal_orders(buys, current_price), []

            else:
                self.logger().warning(
                    f"ANTI-DRAIN: RED ALERT ACTIVE. Conservative order creation only."
                )
                return self._create_conservative_orders(buys, current_price), []

        elif self._alert_level == "ORANGE":
            self.logger().info("ANTI-DRAIN: ORANGE ALERT - Reducing order sizes and increasing spreads")
            return self._create_reduced_orders(buys, current_price), self._create_reduced_orders(sells, current_price)

        elif self._alert_level == "YELLOW":
            self.logger().debug("ANTI-DRAIN: YELLOW ALERT - Enhanced monitoring active")
            return self._create_monitored_orders(buys, current_price), self._create_monitored_orders(sells, current_price)

        # Detect potential attacks or phase transitions
        attack_detected = self._detect_sophisticated_attack(now, current_price)

        if attack_detected:
            self._attack_detected = True
            self._attack_start_time = now
            # Specific response handled in next cycle based on attack_phase

        # Check for attack recovery (price stabilization)
        elif self._attack_detected and self._should_end_attack_mode(now, current_price):
            self.logger().info(
                f"ANTI-DRAIN: Attack appears to be over. Price stabilized at {current_price:.8f}. "
                f"Returning to normal market making in 30 seconds."
            )
            self._attack_detected = False
            self._attack_phase = "COOLDOWN"
            self._attack_start_time = now  # Start cooldown period

        # Normal operation with enhanced monitoring
        return self._apply_enhanced_monitoring(buys, sells, current_price)

    def _update_volume_tracking(self, now: float) -> None:
        """Update volume tracking for attack detection"""
        # Reset volume tracking if needed
        if now - self._last_volume_reset >= self._volume_tracking_window:
            self._volume_history.clear()
            self._last_volume_reset = now

        # Add current timestamp for volume tracking
        self._volume_history.append(now)

    def _detect_sophisticated_attack(self, now: float, current_price: Decimal) -> bool:
        """
        Detect sophisticated multi-phase bot attacks:
        1. PUMP PHASE: Attacker pushes price up to create FOMO
        2. DUMP PHASE: Attacker dumps at high price, then tries to drain our liquidity on the way down

        Returns:
            True if new attack detected, False otherwise
        """
        # IMPROVED: Require fewer price points for faster detection
        if len(self._recent_price_history) < 3:
            return False

        # Calculate price movements over different time windows
        prices_5min = [price for timestamp, price in self._recent_price_history if now - timestamp <= 300]
        prices_2min = [price for timestamp, price in self._recent_price_history if now - timestamp <= 120]
        prices_30sec = [price for timestamp, price in self._recent_price_history if now - timestamp <= 30]

        # IMPROVED: More flexible baseline calculation
        if len(prices_5min) >= 3:
            baseline_price = sum(prices_5min[:3]) / 3  # Average of first 3 prices in 5min window
        elif len(prices_2min) >= 2:
            baseline_price = prices_2min[0]  # Use first price in 2min window
        else:
            baseline_price = self._recent_price_history[0][1]  # Use oldest available price

        # Use current price for recent comparison
        recent_avg = current_price

        # Calculate price change from baseline
        price_change_from_baseline = (current_price - baseline_price) / baseline_price
        recent_change = (current_price - recent_avg) / recent_avg if recent_avg > 0 else Decimal('0')

        # 1. Volume-based detection (still important)
        recent_volume = len([t for t in self._volume_history if now - t <= 60])
        rapid_trades = len([t for t in self._volume_history if now - t <= self._rapid_trade_window])

        # 2. PUMP PHASE DETECTION: Rapid price increase with volume
        if (price_change_from_baseline > self._pump_threshold and
            recent_volume > self._suspicious_volume_threshold):

            if not self._attack_detected:
                self._attack_start_price = baseline_price
                self._attack_phase = "PUMP"
                self.logger().error(
                    f"ANTI-DRAIN: PUMP ATTACK DETECTED! "
                    f"Price pumped {price_change_from_baseline*100:.2f}% from {baseline_price:.8f} to {current_price:.8f}. "
                    f"Volume: {recent_volume} trades/min. Switching to PUMP defense mode."
                )
                return True

        # 3. DUMP PHASE DETECTION: Price falling after pump with high volume
        elif (self._attack_phase == "PUMP" and
              price_change_from_baseline < -self._dump_threshold and
              recent_volume > self._suspicious_volume_threshold):

            self._attack_phase = "DUMP"
            self._pump_peak_price = max(price for _, price in self._recent_price_history[-20:]) if len(self._recent_price_history) >= 20 else current_price
            self.logger().error(
                f"ANTI-DRAIN: DUMP PHASE DETECTED! "
                f"Price dumping from peak {self._pump_peak_price:.8f} to {current_price:.8f}. "
                f"This is the liquidity drain attempt! Switching to DUMP defense mode."
            )
            return True

        # 4. Rapid trading detection (sandwich attacks)
        elif rapid_trades > self._max_trades_per_window:
            if not self._attack_detected:
                self._attack_start_price = current_price
                self._attack_phase = "RAPID"
                self.logger().warning(
                    f"ANTI-DRAIN: RAPID TRADING ATTACK: {rapid_trades} trades in {self._rapid_trade_window}s. "
                    f"Potential sandwich attack detected."
                )
                return True

        return False

    def _should_end_attack_mode(self, now: float, current_price: Decimal) -> bool:
        """
        Determine if attack mode should end based on price stabilization

        Args:
            now: Current timestamp
            current_price: Current market price

        Returns:
            True if attack mode should end, False otherwise
        """
        # Need at least 2 minutes of data to assess stabilization
        if now - self._attack_start_time < 120:
            return False

        # Get recent price data (last 2 minutes)
        recent_prices = [price for timestamp, price in self._recent_price_history
                        if now - timestamp <= 120]

        if len(recent_prices) < 10:
            return False

        # Check for price stabilization (low volatility)
        price_range = max(recent_prices) - min(recent_prices)
        avg_price = sum(recent_prices) / len(recent_prices)
        volatility = price_range / avg_price if avg_price > 0 else Decimal('1')

        # Check volume normalization
        recent_volume = len([t for t in self._volume_history if now - t <= 120])
        volume_per_minute = recent_volume / 2  # 2 minutes of data

        # Attack is over if:
        # 1. Price volatility is low (< 1% range in 2 minutes)
        # 2. Volume has normalized (< suspicious threshold)
        # 3. No rapid trading patterns
        if (volatility < Decimal('0.01') and
            volume_per_minute < self._suspicious_volume_threshold and
            recent_volume < self._max_trades_per_window):

            self.logger().info(
                f"ANTI-DRAIN: Attack recovery detected - "
                f"Volatility: {volatility*100:.2f}%, Volume: {volume_per_minute:.0f}/min, "
                f"Price stabilized around {current_price:.8f}"
            )
            return True

        return False

    def _create_pump_defense_orders(self, buys: List[PriceSize], current_price: Decimal) -> List[PriceSize]:
        """
        PUMP PHASE DEFENSE: Create buy orders only at/below attack start price

        Strategy: Don't chase the pump! Only buy at reasonable levels.

        Args:
            buys: Original buy orders
            current_price: Current market price

        Returns:
            List of defensive buy orders
        """
        if not buys or self._attack_start_price <= 0:
            return []

        # Only create buy orders at or below attack start price + small buffer
        max_buy_price = self._attack_start_price * (1 + self._max_buy_distance_from_attack_start)

        defensive_buys = []
        for buy in buys:
            if buy.price <= max_buy_price:
                # Keep original buy orders that are at reasonable levels
                defensive_buys.append(buy)
            else:
                # Replace high buy orders with orders at attack start level
                safe_price = min(buy.price, max_buy_price)
                defensive_buys.append(PriceSize(safe_price, buy.size))

        self.logger().info(
            f"ANTI-DRAIN PUMP: Creating {len(defensive_buys)} buy orders at/below attack start level "
            f"(max: {max_buy_price:.8f}, current: {current_price:.8f}, attack start: {self._attack_start_price:.8f})"
        )

        return defensive_buys

    def _create_pump_phase_sells(self, sells: List[PriceSize], current_price: Decimal) -> List[PriceSize]:
        """
        PUMP PHASE SELLS: Increase sell liquidity during pump to absorb buying pressure

        Strategy: Provide more liquidity at higher prices during pump

        Args:
            sells: Original sell orders
            current_price: Current market price

        Returns:
            List of enhanced sell orders
        """
        if not sells:
            return []

        # Scale up sell order sizes during pump phase
        enhanced_sells = []
        for sell in sells:
            enhanced_size = sell.size * self._liquidity_scaling_factor
            enhanced_sells.append(PriceSize(sell.price, enhanced_size))

        self.logger().info(
            f"ANTI-DRAIN PUMP: Scaling up {len(enhanced_sells)} sell orders by {self._liquidity_scaling_factor}x "
            f"to absorb pump buying pressure"
        )

        return enhanced_sells

    def _create_conservative_orders(self, buys: List[PriceSize], current_price: Decimal) -> List[PriceSize]:
        """
        Create conservative buy orders during general attack cooldown

        Args:
            buys: Original buy orders
            current_price: Current market price

        Returns:
            List of conservative buy orders
        """
        if not buys:
            return []

        # Create smaller, more conservative buy orders
        conservative_buys = []
        for buy in buys:
            # Reduce size by half and lower price slightly
            conservative_price = buy.price * Decimal('0.995')  # 0.5% lower
            conservative_size = buy.size * Decimal('0.5')  # Half size
            conservative_buys.append(PriceSize(conservative_price, conservative_size))

        self.logger().info(
            f"ANTI-DRAIN: Creating {len(conservative_buys)} conservative buy orders "
            f"(50% size, 0.5% lower prices)"
        )

        return conservative_buys

    def _create_minimal_orders(self, buys: List[PriceSize], current_price: Decimal) -> List[PriceSize]:
        """Create minimal orders during coordinated attacks"""
        if not buys:
            return []

        # Create only one small buy order far below current price
        minimal_price = current_price * Decimal('0.98')  # 2% below current
        minimal_size = buys[0].size * Decimal('0.1')  # 10% of original size

        self.logger().info(
            f"ANTI-DRAIN: Creating minimal order - "
            f"Buy {minimal_size:.2f} at {minimal_price:.8f} (2% below current)"
        )

        return [PriceSize(minimal_price, minimal_size)]

    def _create_reduced_orders(self, orders: List[PriceSize], current_price: Decimal) -> List[PriceSize]:
        """Create reduced orders during ORANGE alert"""
        if not orders:
            return []

        reduced_orders = []
        for order in orders:
            # Reduce size by 50% and adjust price to be more conservative
            reduced_size = order.size * Decimal('0.5')

            # Make prices more conservative (further from current price)
            if order.price < current_price:  # Buy order
                reduced_price = order.price * Decimal('0.995')  # 0.5% lower
            else:  # Sell order
                reduced_price = order.price * Decimal('1.005')  # 0.5% higher

            reduced_orders.append(PriceSize(reduced_price, reduced_size))

        self.logger().debug(
            f"ANTI-DRAIN: Created {len(reduced_orders)} reduced orders "
            f"(50% size, more conservative prices)"
        )

        return reduced_orders

    def _create_monitored_orders(self, orders: List[PriceSize], current_price: Decimal) -> List[PriceSize]:
        """Create monitored orders during YELLOW alert"""
        if not orders:
            return []

        # Reduce size slightly but keep normal pricing
        monitored_orders = []
        for order in orders:
            monitored_size = order.size * Decimal('0.8')  # 20% reduction
            monitored_orders.append(PriceSize(order.price, monitored_size))

        self.logger().debug(
            f"ANTI-DRAIN: Created {len(monitored_orders)} monitored orders "
            f"(80% size, enhanced monitoring)"
        )

        return monitored_orders

    def _apply_enhanced_monitoring(self, buys: List[PriceSize], sells: List[PriceSize], current_price: Decimal) -> Tuple[List[PriceSize], List[PriceSize]]:
        """
        Apply enhanced monitoring during normal operation

        Args:
            buys: Original buy orders
            sells: Original sell orders
            current_price: Current market price

        Returns:
            Tuple of (monitored_buys, monitored_sells)
        """
        # Check for order imbalance
        total_buy_value = sum(buy.size * buy.price for buy in buys)
        total_sell_value = sum(sell.size * sell.price for sell in sells)

        # Calculate imbalance ratio (buy_value / sell_value)
        # High ratio = more buying pressure (good for own token)
        # Low ratio = more selling pressure (bad for own token)
        if total_sell_value > 0 and total_buy_value > 0:
            imbalance_ratio = total_buy_value / total_sell_value
        elif total_sell_value == 0 and total_buy_value > 0:
            imbalance_ratio = Decimal('999')  # Very high if no sells (good for own token)
        elif total_buy_value == 0 and total_sell_value > 0:
            imbalance_ratio = Decimal('0.001')  # Very low if no buys (bad for own token)
        else:
            imbalance_ratio = Decimal('1')  # Neutral if both are zero

        # If too much selling pressure (low ratio), reduce sell orders
        if total_sell_value > 0 and imbalance_ratio < (1 / self._max_order_imbalance_ratio):
            self.logger().warning(
                f"ANTI-DRAIN: High sell pressure detected (ratio: {imbalance_ratio:.2f}). "
                f"Reducing sell orders to protect token price."
            )
            # Keep only the highest priced sell orders
            sells = sorted(sells, key=lambda x: x.price, reverse=True)[:1]

        # If too much buying pressure, it's probably fine for own token, but log it
        elif total_buy_value > 0 and imbalance_ratio > self._max_order_imbalance_ratio:
            self.logger().info(
                f"ANTI-DRAIN: High buy pressure detected (ratio: {imbalance_ratio:.2f}). "
                f"This is generally good for own token price support."
            )

        self.logger().debug(
            f"ANTI-DRAIN: Order monitoring - "
            f"Buy value: {total_buy_value:.4f}, Sell value: {total_sell_value:.4f}, "
            f"Ratio: {imbalance_ratio:.2f}, Buys: {len(buys)}, Sells: {len(sells)}"
        )

        return buys, sells

    cdef tuple c_get_adjusted_available_balance(self, list orders):
        """
        Calculates the available balance, plus the amount attributed to orders.
        :return: (base amount, quote amount) in Decimal
        """
        cdef:
            ExchangeBase market = self._market_info.market
            object base_balance = market.c_get_available_balance(self.base_asset)
            object quote_balance = market.c_get_available_balance(self.quote_asset)

        # Log raw balances before adjustment
        self.logger().info(
            f"BALANCE_TRACKING: Raw balances - Base ({self.base_asset}): {base_balance}, "
            f"Quote ({self.quote_asset}): {quote_balance}"
        )

        # Track each order's contribution to the adjusted balance
        order_contributions = []
        for order in orders:
            from hummingbot.core.data_type.common import TradeType
            is_buy = order.trade_type == TradeType.BUY if hasattr(order, 'trade_type') else order.is_buy
            if is_buy:
                quote_balance += order.quantity * order.price
                order_contributions.append(
                    f"Buy order {order.client_order_id}: +{order.quantity * order.price} {self.quote_asset}"
                )
            else:
                base_balance += order.quantity
                order_contributions.append(
                    f"Sell order {order.client_order_id}: +{order.quantity} {self.base_asset}"
                )

        # Log detailed order adjustment information
        if order_contributions:
            self.logger().info(f"BALANCE_TRACKING: Adjusting for {len(orders)} active orders:")
            for contribution in order_contributions:
                self.logger().info(f"BALANCE_TRACKING: {contribution}")

        # Log final adjusted balances
        self.logger().info(
            f"BALANCE_TRACKING: Final adjusted balances - Base ({self.base_asset}): {base_balance}, "
            f"Quote ({self.quote_asset}): {quote_balance}"
        )

        return base_balance, quote_balance

    cdef c_apply_order_levels_modifiers(self, proposal):
        self.c_apply_price_band(proposal)
        if self.moving_price_band_enabled:
            self.c_apply_moving_price_band(proposal)
        if self._ping_pong_enabled:
            self.c_apply_ping_pong(proposal)

    cdef c_apply_price_band(self, proposal):
        if self._price_ceiling > 0 and self.get_price() >= self._price_ceiling:
            proposal.buys = []
        if self._price_floor > 0 and self.get_price() <= self._price_floor:
            proposal.sells = []

    cdef c_apply_moving_price_band(self, proposal):
        price = self.get_price()
        self._moving_price_band.check_and_update_price_band(
            self.current_timestamp, price)
        if self._moving_price_band.check_price_ceiling_exceeded(price):
            proposal.buys = []
        if self._moving_price_band.check_price_floor_exceeded(price):
            proposal.sells = []

    cdef c_apply_ping_pong(self, object proposal):
        self._ping_pong_warning_lines = []
        if self._filled_buys_balance == self._filled_sells_balance:
            self._filled_buys_balance = self._filled_sells_balance = 0
        if self._filled_buys_balance > 0:
            proposal.buys = proposal.buys[self._filled_buys_balance:]
            self._ping_pong_warning_lines.extend(
                [f"  Ping-pong removed {self._filled_buys_balance} buy orders."]
            )
        if self._filled_sells_balance > 0:
            proposal.sells = proposal.sells[self._filled_sells_balance:]
            self._ping_pong_warning_lines.extend(
                [f"  Ping-pong removed {self._filled_sells_balance} sell orders."]
            )

    cdef c_apply_order_price_modifiers(self, object proposal):
        if self._order_optimization_enabled:
            self.c_apply_order_optimization(proposal)

        if self._add_transaction_costs_to_orders:
            self.c_apply_add_transaction_costs(proposal)

    cdef c_apply_order_size_modifiers(self, object proposal):
        if self._inventory_skew_enabled:
            self.c_apply_inventory_skew(proposal)

    cdef c_apply_inventory_skew(self, object proposal):
        cdef:
            ExchangeBase market = self._market_info.market
            object bid_adj_ratio
            object ask_adj_ratio
            object size

        base_balance, quote_balance = self.c_get_adjusted_available_balance(self.active_orders)

        total_order_size = calculate_total_order_size(self._order_amount, self._order_level_amount, self._order_levels)
        bid_ask_ratios = c_calculate_bid_ask_ratios_from_base_asset_ratio(
            float(base_balance),
            float(quote_balance),
            float(self.get_price()),
            float(self._inventory_target_base_pct),
            float(total_order_size * self._inventory_range_multiplier)
        )
        bid_adj_ratio = Decimal(bid_ask_ratios.bid_ratio)
        ask_adj_ratio = Decimal(bid_ask_ratios.ask_ratio)

        for buy in proposal.buys:
            size = buy.size * bid_adj_ratio
            size = market.c_quantize_order_amount(self.trading_pair, size)
            buy.size = size

        for sell in proposal.sells:
            size = sell.size * ask_adj_ratio
            size = market.c_quantize_order_amount(self.trading_pair, size, sell.price)
            sell.size = size

    cdef c_apply_depth_optimization(self, object proposal):
        """
        Apply ±2% depth optimization to maximize liquidity within the target spread range.

        This method:
        1. Identifies orders within the ±2% range from mid price
        2. Adjusts order sizes to maximize depth within this range
        3. Applies dynamic adjustments based on market conditions
        4. Maintains existing bot protection and DEX coordination
        """
        if not hasattr(self, '_enable_depth_optimization') or not self._enable_depth_optimization:
            return

        # Defensive checks for all required attributes
        if not hasattr(self, '_depth_optimization_range_pct'):
            self.logger().warning("DEPTH_OPTIMIZATION: Missing _depth_optimization_range_pct, using default 2%")
            self._depth_optimization_range_pct = Decimal("0.02")
        if not hasattr(self, '_depth_budget_allocation_pct'):
            self.logger().warning("DEPTH_OPTIMIZATION: Missing _depth_budget_allocation_pct, using default 80%")
            self._depth_budget_allocation_pct = Decimal("0.80")
        if not hasattr(self, '_enable_dynamic_depth_adjustment'):
            self._enable_dynamic_depth_adjustment = False

        cdef:
            object current_price = self.get_price()
            object range_threshold = current_price * self._depth_optimization_range_pct
            object upper_range = current_price + range_threshold
            object lower_range = current_price - range_threshold
            object total_budget_bid = Decimal("0")
            object total_budget_ask = Decimal("0")
            object depth_budget_bid = Decimal("0")
            object depth_budget_ask = Decimal("0")
            object volatility_multiplier = Decimal("1.0")

        try:
            # Calculate total budget for each side
            for buy in proposal.buys:
                total_budget_bid += buy.size
            for sell in proposal.sells:
                total_budget_ask += sell.size

            # Apply dynamic depth adjustment based on volatility if enabled
            if self._enable_dynamic_depth_adjustment:
                volatility_multiplier = self._get_volatility_depth_multiplier()

            # Calculate depth-optimized budget allocation
            depth_budget_bid = total_budget_bid * self._depth_budget_allocation_pct * volatility_multiplier
            depth_budget_ask = total_budget_ask * self._depth_budget_allocation_pct * volatility_multiplier

            # Optimize bid orders (buy orders)
            self._optimize_orders_for_depth(proposal.buys, lower_range, upper_range,
                                          depth_budget_bid, total_budget_bid, True)

            # Optimize ask orders (sell orders)
            self._optimize_orders_for_depth(proposal.sells, lower_range, upper_range,
                                          depth_budget_ask, total_budget_ask, False)

            # Log depth optimization results
            self._log_depth_optimization_results(proposal, current_price, lower_range, upper_range)

        except Exception as e:
            self.logger().error(f"DEPTH_OPTIMIZATION: Error in depth optimization: {str(e)}")

    cdef _optimize_orders_for_depth(self, list orders, object lower_range, object upper_range,
                                   object depth_budget, object total_budget, bint is_buy_side):
        """Optimize order sizes to maximize depth within ±2% range"""
        # Defensive check for minimum order size
        if not hasattr(self, '_min_depth_order_size'):
            self._min_depth_order_size = Decimal("50.0")

        cdef:
            object within_range_budget = Decimal("0")
            object outside_range_budget = Decimal("0")
            list within_range_orders = []
            list outside_range_orders = []
            object size_multiplier = Decimal("1.0")

        # Separate orders within and outside the ±2% range
        for order in orders:
            if lower_range <= order.price <= upper_range:
                within_range_orders.append(order)
                within_range_budget += order.size
            else:
                outside_range_orders.append(order)
                outside_range_budget += order.size

        # Calculate size multiplier for within-range orders
        if within_range_budget > 0 and len(within_range_orders) > 0:
            size_multiplier = depth_budget / within_range_budget

            # Apply size multiplier to within-range orders
            for order in within_range_orders:
                new_size = order.size * size_multiplier
                # Ensure minimum order size
                if new_size >= self._min_depth_order_size:
                    order.size = new_size
                else:
                    # If order becomes too small, remove it
                    orders.remove(order)

        # Adjust outside-range orders with remaining budget
        remaining_budget = total_budget - depth_budget
        if outside_range_budget > 0 and remaining_budget > 0 and len(outside_range_orders) > 0:
            outside_multiplier = remaining_budget / outside_range_budget
            for order in outside_range_orders:
                new_size = order.size * outside_multiplier
                if new_size >= self._min_depth_order_size:
                    order.size = new_size
                else:
                    # Remove orders that become too small
                    orders.remove(order)

    cdef object _get_volatility_depth_multiplier(self):
        """Calculate volatility-based depth multiplier for dynamic adjustment"""
        try:
            # Use existing volatility calculation if available
            if hasattr(self, '_volatility_regime'):
                if self._volatility_regime == "HIGH":
                    return self._volatility_depth_multiplier
                elif self._volatility_regime == "MEDIUM":
                    return (self._volatility_depth_multiplier + Decimal("1.0")) / 2
            return Decimal("1.0")  # Default multiplier
        except Exception:
            return Decimal("1.0")

    cdef _log_depth_optimization_results(self, object proposal, object current_price,
                                       object lower_range, object upper_range):
        """Log the results of depth optimization for monitoring"""
        cdef:
            object total_bid_depth = Decimal("0")
            object total_ask_depth = Decimal("0")
            object within_range_bid_depth = Decimal("0")
            object within_range_ask_depth = Decimal("0")
            int within_range_bid_orders = 0
            int within_range_ask_orders = 0

        try:

            # Calculate depth metrics
            for buy in proposal.buys:
                total_bid_depth += buy.size
                if lower_range <= buy.price <= upper_range:
                    within_range_bid_depth += buy.size
                    within_range_bid_orders += 1

            for sell in proposal.sells:
                total_ask_depth += sell.size
                if lower_range <= sell.price <= upper_range:
                    within_range_ask_depth += sell.size
                    within_range_ask_orders += 1

            # Calculate percentages
            bid_depth_pct = (within_range_bid_depth / total_bid_depth * 100) if total_bid_depth > 0 else Decimal("0")
            ask_depth_pct = (within_range_ask_depth / total_ask_depth * 100) if total_ask_depth > 0 else Decimal("0")

            # Log results every 60 seconds to avoid spam
            current_time = time.time()
            if current_time - self._last_depth_calculation > 60.0:
                self.logger().info(
                    f"DEPTH_OPTIMIZATION: ±2% Range: ${lower_range:.6f} - ${upper_range:.6f} "
                    f"(Mid: ${current_price:.6f})"
                )
                self.logger().info(
                    f"DEPTH_OPTIMIZATION: Bid depth within ±2%: {within_range_bid_depth:.0f} FULA "
                    f"({bid_depth_pct:.1f}%) across {within_range_bid_orders} orders"
                )
                self.logger().info(
                    f"DEPTH_OPTIMIZATION: Ask depth within ±2%: {within_range_ask_depth:.0f} FULA "
                    f"({ask_depth_pct:.1f}%) across {within_range_ask_orders} orders"
                )
                self._last_depth_calculation = current_time

        except Exception as e:
            self.logger().error(f"DEPTH_OPTIMIZATION: Error logging results: {str(e)}")

    def _log_market_depth_before_orders(self):
        """Log current market depth within ±2% range BEFORE placing orders"""
        try:
            current_price = self.get_price()
            if current_price is None or current_price <= 0:
                return

            # Calculate ±2% range
            range_threshold = current_price * Decimal("0.02")  # 2%
            upper_range = current_price + range_threshold
            lower_range = current_price - range_threshold

            # Get current market depth
            market = self._market_info.market
            order_book = market.get_order_book(self.trading_pair)

            if order_book is None:
                return

            # Calculate current market depth within ±2%
            bid_depth_2pct, ask_depth_2pct = self._calculate_market_depth_in_range(
                order_book, lower_range, upper_range, current_price
            )

            self.logger().info(
                f"DEPTH_MONITOR: BEFORE orders - Mid: ${current_price:.6f} | "
                f"±2% Range: ${lower_range:.6f} - ${upper_range:.6f} | "
                f"Current Market Depth: {bid_depth_2pct:.0f} bids / {ask_depth_2pct:.0f} asks"
            )

        except Exception as e:
            self.logger().error(f"DEPTH_MONITOR: Error logging before depth: {str(e)}")

    def _log_market_depth_after_orders(self, proposal):
        """Log market depth within ±2% range AFTER placing orders, including our contribution"""
        try:
            current_price = self.get_price()
            if current_price is None or current_price <= 0:
                return

            # Calculate ±2% range
            range_threshold = current_price * Decimal("0.02")  # 2%
            upper_range = current_price + range_threshold
            lower_range = current_price - range_threshold

            # Get current market depth (including our new orders)
            market = self._market_info.market
            order_book = market.get_order_book(self.trading_pair)

            if order_book is None:
                return

            # Calculate total market depth within ±2% (including our orders)
            total_bid_depth_2pct, total_ask_depth_2pct = self._calculate_market_depth_in_range(
                order_book, lower_range, upper_range, current_price
            )

            # Calculate our contribution to depth within ±2%
            our_bid_depth_2pct = Decimal("0")
            our_ask_depth_2pct = Decimal("0")
            our_bid_orders_2pct = 0
            our_ask_orders_2pct = 0

            for buy in proposal.buys:
                if lower_range <= buy.price <= upper_range:
                    our_bid_depth_2pct += buy.size
                    our_bid_orders_2pct += 1

            for sell in proposal.sells:
                if lower_range <= sell.price <= upper_range:
                    our_ask_depth_2pct += sell.size
                    our_ask_orders_2pct += 1

            # Calculate our percentage contribution
            our_bid_pct = (our_bid_depth_2pct / total_bid_depth_2pct * 100) if total_bid_depth_2pct > 0 else Decimal("0")
            our_ask_pct = (our_ask_depth_2pct / total_ask_depth_2pct * 100) if total_ask_depth_2pct > 0 else Decimal("0")

            self.logger().info(
                f"DEPTH_MONITOR: AFTER orders - Total Market Depth: {total_bid_depth_2pct:.0f} bids / {total_ask_depth_2pct:.0f} asks | "
                f"Our Contribution: {our_bid_depth_2pct:.0f} bids ({our_bid_pct:.1f}%) / {our_ask_depth_2pct:.0f} asks ({our_ask_pct:.1f}%) | "
                f"Our Orders in ±2%: {our_bid_orders_2pct} bids / {our_ask_orders_2pct} asks"
            )

        except Exception as e:
            self.logger().error(f"DEPTH_MONITOR: Error logging after depth: {str(e)}")

    def _calculate_market_depth_in_range(self, order_book, lower_range, upper_range, current_price):
        """Calculate total market depth (in base asset) within the specified price range"""
        try:
            bid_depth = Decimal("0")
            ask_depth = Decimal("0")

            # Calculate bid depth within range
            for bid_entry in order_book.bid_entries():
                # Defensive unpacking - handle both (price, amount) and (price, amount, other) formats
                if len(bid_entry) >= 2:
                    bid_price, bid_amount = Decimal(str(bid_entry[0])), Decimal(str(bid_entry[1]))
                    if lower_range <= bid_price <= current_price:  # Bids below current price
                        bid_depth += bid_amount
                    elif bid_price < lower_range:
                        break  # Bids are sorted descending, so we can stop

            # Calculate ask depth within range
            for ask_entry in order_book.ask_entries():
                # Defensive unpacking - handle both (price, amount) and (price, amount, other) formats
                if len(ask_entry) >= 2:
                    ask_price, ask_amount = Decimal(str(ask_entry[0])), Decimal(str(ask_entry[1]))
                    if current_price <= ask_price <= upper_range:  # Asks above current price
                        ask_depth += ask_amount
                    elif ask_price > upper_range:
                        break  # Asks are sorted ascending, so we can stop

            return bid_depth, ask_depth

        except Exception as e:
            self.logger().error(f"DEPTH_MONITOR: Error calculating market depth: {str(e)}")
            return Decimal("0"), Decimal("0")

    def _log_periodic_depth_status(self):
        """Log periodic ±2% depth status for ongoing monitoring"""
        try:
            current_price = self.get_price()
            if current_price is None or current_price <= 0:
                return

            # Calculate ±2% range
            range_threshold = current_price * Decimal("0.02")  # 2%
            upper_range = current_price + range_threshold
            lower_range = current_price - range_threshold

            # Get current market depth
            market = self._market_info.market
            order_book = market.get_order_book(self.trading_pair)

            if order_book is None:
                return

            # Calculate current market depth within ±2%
            total_bid_depth_2pct, total_ask_depth_2pct = self._calculate_market_depth_in_range(
                order_book, lower_range, upper_range, current_price
            )

            # Calculate our contribution from active orders
            our_bid_depth_2pct = Decimal("0")
            our_ask_depth_2pct = Decimal("0")
            our_bid_orders_2pct = 0
            our_ask_orders_2pct = 0

            for order in self.active_orders:
                if order.is_buy and lower_range <= order.price <= upper_range:
                    our_bid_depth_2pct += order.quantity
                    our_bid_orders_2pct += 1
                elif not order.is_buy and lower_range <= order.price <= upper_range:
                    our_ask_depth_2pct += order.quantity
                    our_ask_orders_2pct += 1

            # Calculate our percentage contribution
            our_bid_pct = (our_bid_depth_2pct / total_bid_depth_2pct * 100) if total_bid_depth_2pct > 0 else Decimal("0")
            our_ask_pct = (our_ask_depth_2pct / total_ask_depth_2pct * 100) if total_ask_depth_2pct > 0 else Decimal("0")

            # Calculate total USD value of depth (approximate)
            total_depth_usd = (total_bid_depth_2pct + total_ask_depth_2pct) * current_price
            our_depth_usd = (our_bid_depth_2pct + our_ask_depth_2pct) * current_price

            self.logger().info("=" * 80)
            self.logger().info("📊 ±2% DEPTH STATUS REPORT")
            self.logger().info("=" * 80)
            self.logger().info(f"💰 Current Price: ${current_price:.6f}")
            self.logger().info(f"📏 ±2% Range: ${lower_range:.6f} - ${upper_range:.6f}")
            self.logger().info(f"🌊 Total Market Depth: {total_bid_depth_2pct:.0f} bids / {total_ask_depth_2pct:.0f} asks (~${total_depth_usd:.0f})")
            self.logger().info(f"🤖 Our Contribution: {our_bid_depth_2pct:.0f} bids ({our_bid_pct:.1f}%) / {our_ask_depth_2pct:.0f} asks ({our_ask_pct:.1f}%) (~${our_depth_usd:.0f})")
            self.logger().info(f"📋 Our Active Orders in ±2%: {our_bid_orders_2pct} bids / {our_ask_orders_2pct} asks")

            # Add depth quality assessment
            if total_depth_usd >= self._target_cumulative_depth_usd:
                self.logger().info("✅ DEPTH STATUS: Target depth achieved!")
            elif total_depth_usd >= self._target_cumulative_depth_usd * Decimal("0.7"):
                self.logger().info("⚠️  DEPTH STATUS: Approaching target depth")
            else:
                self.logger().info("🔴 DEPTH STATUS: Below target depth - consider increasing orders")

            self.logger().info("=" * 80)

        except Exception as e:
            self.logger().error(f"DEPTH_MONITOR: Error in periodic depth status: {str(e)}")

    def _check_own_token_safety(self, current_price: Decimal) -> bool:
        """
        OWN-TOKEN SAFETY: Check safety limits for own-token market making

        Returns True if it's safe to continue, False if emergency stop needed
        """
        if not hasattr(self, '_enable_own_token_safety') or not self._enable_own_token_safety:
            return True

        try:
            current_time = time.time()

            # Reset daily tracking at midnight
            if current_time - self._daily_reset_time > 86400:  # 24 hours
                self._daily_pnl_usd = Decimal("0")
                self._daily_reset_time = current_time
                self.logger().info("OWN_TOKEN_SAFETY: Daily P&L reset")

            # Reset 8-hour tracking
            if current_time - self._eight_hour_reset_time > 28800:  # 8 hours
                # Calculate 8-hour average prices before reset
                if self._eight_hour_buy_total > 0 and self._eight_hour_sell_total > 0:
                    avg_buy_price = self._eight_hour_buy_value / self._eight_hour_buy_total
                    avg_sell_price = self._eight_hour_sell_value / self._eight_hour_sell_total

                    if avg_buy_price > avg_sell_price:
                        self.logger().warning(
                            f"OWN_TOKEN_SAFETY: 8-hour average buy price ({avg_buy_price:.6f}) > "
                            f"average sell price ({avg_sell_price:.6f}). This indicates systematic loss."
                        )
                        self._safety_violations += 1
                    else:
                        self.logger().info(
                            f"OWN_TOKEN_SAFETY: 8-hour averages healthy - "
                            f"Buy: {avg_buy_price:.6f}, Sell: {avg_sell_price:.6f}"
                        )

                # Reset 8-hour tracking
                self._eight_hour_buy_total = Decimal("0")
                self._eight_hour_sell_total = Decimal("0")
                self._eight_hour_buy_value = Decimal("0")
                self._eight_hour_sell_value = Decimal("0")
                self._eight_hour_reset_time = current_time

            # Check daily loss limit
            if self._daily_pnl_usd < -self._daily_loss_limit_usd:
                self.logger().error(
                    f"OWN_TOKEN_SAFETY: Daily loss limit exceeded! "
                    f"P&L: ${self._daily_pnl_usd:.2f}, Limit: ${-self._daily_loss_limit_usd:.2f}"
                )
                self._emergency_stop_active = True
                return False

            # Check safety violations
            if self._safety_violations >= self._max_safety_violations:
                self.logger().error(
                    f"OWN_TOKEN_SAFETY: Too many safety violations ({self._safety_violations}). "
                    f"Emergency stop activated."
                )
                self._emergency_stop_active = True
                return False

            return True

        except Exception as e:
            self.logger().error(f"OWN_TOKEN_SAFETY: Error in safety check: {str(e)}")
            return False

    def _validate_own_token_order(self, is_buy: bool, price: Decimal, amount: Decimal, current_price: Decimal) -> Tuple[bool, str]:
        """
        OWN-TOKEN SAFETY: Validate individual orders for own-token market making

        Returns (is_safe, reason)
        """
        if not hasattr(self, '_enable_own_token_safety') or not self._enable_own_token_safety:
            return True, ""

        try:
            # Check for extreme price deviations (order-level stop loss)
            price_deviation = abs(price - current_price) / current_price

            if price_deviation > self._order_loss_limit_pct:
                reason = (f"Order price deviation {price_deviation*100:.2f}% exceeds limit "
                         f"{self._order_loss_limit_pct*100:.2f}%")
                return False, reason

            # For buy orders: warn if buying significantly above current price
            if is_buy and price > current_price * (1 + self._order_loss_limit_pct):
                reason = (f"Buy order price {price:.6f} too high vs current {current_price:.6f} "
                         f"(>{self._order_loss_limit_pct*100:.1f}% above)")
                return False, reason

            # For sell orders: warn if selling significantly below current price
            if not is_buy and price < current_price * (1 - self._order_loss_limit_pct):
                reason = (f"Sell order price {price:.6f} too low vs current {current_price:.6f} "
                         f"(>{self._order_loss_limit_pct*100:.1f}% below)")
                return False, reason

            return True, ""

        except Exception as e:
            self.logger().error(f"OWN_TOKEN_SAFETY: Error validating order: {str(e)}")
            return False, f"Validation error: {str(e)}"

    def _update_own_token_tracking(self, is_buy: bool, price: Decimal, amount: Decimal):
        """Update tracking for own-token safety monitoring"""
        if not hasattr(self, '_enable_own_token_safety') or not self._enable_own_token_safety:
            return

        try:
            value = price * amount

            if is_buy:
                self._eight_hour_buy_total += amount
                self._eight_hour_buy_value += value
            else:
                self._eight_hour_sell_total += amount
                self._eight_hour_sell_value += value

            # Estimate P&L impact (simplified)
            if is_buy:
                # Buying increases cost basis
                self._daily_pnl_usd -= value * Decimal("0.001")  # Assume small cost
            else:
                # Selling generates revenue
                self._daily_pnl_usd += value * Decimal("0.001")  # Assume small profit

        except Exception as e:
            self.logger().error(f"OWN_TOKEN_SAFETY: Error updating tracking: {str(e)}")

    def adjusted_available_balance_for_orders_budget_constrain(self):
        # Step 1: Get hanging orders from the tracker
        candidate_hanging_orders = self.hanging_orders_tracker.candidate_hanging_orders_from_pairs()

        # Step 2: Initialize collection for active orders
        non_hanging = []
        active_orders_from_tracker = []
        all_orders = []

        # Step 3: Get orders from the primary order tracker
        if self.market_info in self._sb_order_tracker.get_limit_orders():
            all_orders = list(self._sb_order_tracker.get_limit_orders()[self.market_info].values())
            active_orders_from_tracker = [order for order in all_orders
                                 if not self._hanging_orders_tracker.is_order_id_in_hanging_orders(order.client_order_id)]

        # Step 4: Get orders from the active_orders property (this is a backup source of truth)
        active_orders_property = getattr(self, 'active_orders', [])

        # Step 5: DISABLED - Direct exchange fetch was causing InFlightOrder.is_buy errors
        # This was preventing proper balance tracking and order cancellation
        exchange_orders = []
        self.logger().debug("BALANCE_TRACKING: Direct exchange fetch disabled to prevent InFlightOrder errors")

        # Step 6: Merge orders from all sources, prioritizing orders from the tracker
        # First add all orders from tracker
        non_hanging = active_orders_from_tracker.copy()

        # Then add active_orders that aren't already in the list (based on client_order_id)
        tracker_order_ids = {order.client_order_id for order in active_orders_from_tracker}
        for order in active_orders_property:
            if order.client_order_id not in tracker_order_ids:
                non_hanging.append(order)
                tracker_order_ids.add(order.client_order_id)

        # Add any additional orders found directly from the exchange
        for order in exchange_orders:
            if order.client_order_id not in tracker_order_ids:
                non_hanging.append(order)
                tracker_order_ids.add(order.client_order_id)

        # Step 7: Remove hanging orders from the final list
        all_non_hanging_orders = list(set(non_hanging) - set(candidate_hanging_orders))

        # Step 8: CRITICAL FIX - Remove orders that are in the process of being cancelled
        # This prevents "ghost orders" from affecting balance calculations
        in_flight_cancels = set()
        if hasattr(self, '_sb_order_tracker') and hasattr(self._sb_order_tracker, 'in_flight_cancels'):
            in_flight_cancels = set(self._sb_order_tracker.in_flight_cancels.keys())

        # Filter out orders that are being cancelled
        orders_before_cancel_filter = len(all_non_hanging_orders)
        all_non_hanging_orders = [
            order for order in all_non_hanging_orders
            if order.client_order_id not in in_flight_cancels
        ]
        orders_after_cancel_filter = len(all_non_hanging_orders)

        if orders_before_cancel_filter != orders_after_cancel_filter:
            cancelled_order_ids = [oid for oid in in_flight_cancels]
            self.logger().info(
                f"BALANCE_TRACKING: Filtered out {orders_before_cancel_filter - orders_after_cancel_filter} orders "
                f"that are being cancelled: {', '.join(cancelled_order_ids)}"
            )

        # Log what orders are being excluded from the adjustment
        self.logger().info(
            f"BALANCE_TRACKING: Calculating adjusted balance for budget constraint. "
            f"Orders from tracker: {len(active_orders_from_tracker)}, "
            f"Orders from active_orders property: {len(active_orders_property)}, "
            f"Orders from direct exchange fetch: {len(exchange_orders)}, "
            f"Combined active orders: {len(non_hanging)}, "
            f"Orders being cancelled: {len(in_flight_cancels)}, "
            f"Final orders for balance adjustment: {len(all_non_hanging_orders)}"
        )

        # Log the order IDs being considered for adjustment
        if all_non_hanging_orders:
            order_ids = [order.client_order_id for order in all_non_hanging_orders]
            self.logger().info(f"BALANCE_TRACKING: Order IDs considered for balance adjustment: {', '.join(order_ids)}")

        return self.c_get_adjusted_available_balance(all_non_hanging_orders)

    def _get_balances_with_retry(self, max_retries=3, retry_delay=1.0):
        """Helper method to get balances with retry logic"""
        last_error = None
        for attempt in range(max_retries):
            try:
                base_balance, quote_balance = self.adjusted_available_balance_for_orders_budget_constrain()
                if base_balance is not None and quote_balance is not None:
                    return base_balance, quote_balance
                else:
                    raise ValueError("Invalid balance values returned")
            except Exception as e:
                last_error = e
                self.logger().warning(
                    f"BALANCE_RETRY: Attempt {attempt + 1}/{max_retries} failed to get balances: {str(e)}"
                )
                if attempt < max_retries - 1:  # Don't sleep on the last attempt
                    time.sleep(retry_delay)

        # If we get here, all retries failed
        error_msg = f"Failed to get valid balances after {max_retries} attempts: {str(last_error)}"
        self.logger().error(error_msg)
        # Return zeros to prevent order creation with invalid balances
        return Decimal('0'), Decimal('0')

    cdef c_apply_budget_constraint(self, object proposal):
        cdef:
            ExchangeBase market = self._market_info.market
            object quote_size
            object base_size
            object adjusted_amount
            object original_size
            int retry_count = 0
            bint balances_valid = False

        # Get balances with retry logic
        base_balance, quote_balance = self._get_balances_with_retry(
            max_retries=3,
            retry_delay=1.0
        )

        # Log budget constraint starting to be applied
        self.logger().info(
            f"BUDGET_CONSTRAINT: Starting budget constraint with Base balance: {base_balance} {self.base_asset}, "
            f"Quote balance: {quote_balance} {self.quote_asset}"
        )

        # Log original buy proposals
        if proposal.buys:
            buy_proposals = [f"({buy.size} {self.base_asset} @ {buy.price} {self.quote_asset})" for buy in proposal.buys]
            self.logger().info(f"BUDGET_CONSTRAINT: Original BUY proposals: {', '.join(buy_proposals)}")

        for buy in proposal.buys:
            original_size = buy.size  # Store original size before any adjustments
            buy_fee = market.c_get_fee(self.base_asset, self.quote_asset, OrderType.LIMIT, TradeType.BUY,
                                       buy.size, buy.price)
            quote_size = buy.size * buy.price * (Decimal(1) + buy_fee.percent)

            # Log the required quote size for this order
            self.logger().info(
                f"BUDGET_CONSTRAINT: BUY order requires {quote_size} {self.quote_asset} "
                f"(size: {buy.size} {self.base_asset}, price: {buy.price} {self.quote_asset}, fee: {buy_fee.percent})"
            )

            # Adjust buy order size to use remaining balance if less than the order amount
            if quote_balance < quote_size:
                self.logger().warning(
                    f"BUDGET_CONSTRAINT: Insufficient quote balance ({quote_balance} {self.quote_asset}) "
                    f"for buy order requiring {quote_size} {self.quote_asset}"
                )
                adjusted_amount = quote_balance / (buy.price * (Decimal("1") + buy_fee.percent))
                adjusted_amount = market.c_quantize_order_amount(self.trading_pair, adjusted_amount)
                buy.size = adjusted_amount

                # Log the adjustment that was made
                self.logger().warning(
                    f"BUDGET_CONSTRAINT: Reduced buy order size from {original_size} to {buy.size} {self.base_asset} "
                    f"due to insufficient balance"
                )

                quote_balance = s_decimal_zero
            elif quote_balance == s_decimal_zero:
                self.logger().warning(
                    f"BUDGET_CONSTRAINT: Zero quote balance, setting buy order size to zero"
                )
                buy.size = s_decimal_zero
            else:
                self.logger().info(
                    f"BUDGET_CONSTRAINT: Sufficient balance for buy order, using {quote_size} of {quote_balance} {self.quote_asset}"
                )
                quote_balance -= quote_size

        proposal.buys = [o for o in proposal.buys if o.size > 0]

        # Log final buy proposals after constraint
        if proposal.buys:
            buy_proposals = [f"({buy.size} {self.base_asset} @ {buy.price} {self.quote_asset})" for buy in proposal.buys]
            self.logger().info(f"BUDGET_CONSTRAINT: Final BUY proposals after constraint: {', '.join(buy_proposals)}")
        else:
            self.logger().warning("BUDGET_CONSTRAINT: No buy orders remaining after budget constraint")

        # Log original sell proposals
        if proposal.sells:
            sell_proposals = [f"({sell.size} {self.base_asset} @ {sell.price} {self.quote_asset})" for sell in proposal.sells]
            self.logger().info(f"BUDGET_CONSTRAINT: Original SELL proposals: {', '.join(sell_proposals)}")

        for sell in proposal.sells:
            original_size = sell.size  # Store original size before any adjustments
            base_size = sell.size

            # Log the required base size for this order
            self.logger().info(
                f"BUDGET_CONSTRAINT: SELL order requires {base_size} {self.base_asset} "
                f"(size: {sell.size} {self.base_asset}, price: {sell.price} {self.quote_asset})"
            )

            # Adjust sell order size to use remaining balance if less than the order amount
            if base_balance < base_size:
                self.logger().warning(
                    f"BUDGET_CONSTRAINT: Insufficient base balance ({base_balance} {self.base_asset}) "
                    f"for sell order requiring {base_size} {self.base_asset}"
                )
                adjusted_amount = market.c_quantize_order_amount(self.trading_pair, base_balance)
                sell.size = adjusted_amount

                # Log the adjustment that was made
                self.logger().warning(
                    f"BUDGET_CONSTRAINT: Reduced sell order size from {original_size} to {sell.size} {self.base_asset} "
                    f"due to insufficient balance"
                )

                base_balance = s_decimal_zero
            elif base_balance == s_decimal_zero:
                self.logger().warning(
                    f"BUDGET_CONSTRAINT: Zero base balance, setting sell order size to zero"
                )
                sell.size = s_decimal_zero
            else:
                self.logger().info(
                    f"BUDGET_CONSTRAINT: Sufficient balance for sell order, using {base_size} of {base_balance} {self.base_asset}"
                )
                base_balance -= base_size

        proposal.sells = [o for o in proposal.sells if o.size > 0]

        # Log final sell proposals after constraint
        if proposal.sells:
            sell_proposals = [f"({sell.size} {self.base_asset} @ {sell.price} {self.quote_asset})" for sell in proposal.sells]
            self.logger().info(f"BUDGET_CONSTRAINT: Final SELL proposals after constraint: {', '.join(sell_proposals)}")
        else:
            self.logger().warning("BUDGET_CONSTRAINT: No sell orders remaining after budget constraint")

    cdef c_filter_out_takers(self, object proposal):
        cdef:
            ExchangeBase market = self._market_info.market
            list new_buys = []
            list new_sells = []
        top_ask = market.c_get_price(self.trading_pair, True)
        if not top_ask.is_nan():
            proposal.buys = [buy for buy in proposal.buys if buy.price < top_ask]
        top_bid = market.c_get_price(self.trading_pair, False)
        if not top_bid.is_nan():
            proposal.sells = [sell for sell in proposal.sells if sell.price > top_bid]

    # Compare the market price with the top bid and top ask price
    cdef c_apply_order_optimization(self, object proposal):
        cdef:
            ExchangeBase market = self._market_info.market
            object own_buy_size = s_decimal_zero
            object own_sell_size = s_decimal_zero

        for order in self.active_orders:
            if order.is_buy:
                own_buy_size = order.quantity
            else:
                own_sell_size = order.quantity

        if len(proposal.buys) > 0:
            # Get the top bid price in the market using order_optimization_depth and your buy order volume
            top_bid_price = self._market_info.get_price_for_volume(
                False, self._bid_order_optimization_depth + own_buy_size).result_price
            price_quantum = market.c_get_order_price_quantum(
                self.trading_pair,
                top_bid_price
            )
            # Get the price above the top bid
            price_above_bid = (ceil(top_bid_price / price_quantum) + 1) * price_quantum

            # If the price_above_bid is lower than the price suggested by the top pricing proposal,
            # lower the price and from there apply the order_level_spread to each order in the next levels
            proposal.buys = sorted(proposal.buys, key = lambda p: p.price, reverse = True)
            lower_buy_price = min(proposal.buys[0].price, price_above_bid)
            for i, proposed in enumerate(proposal.buys):
                if self._split_order_levels_enabled:
                    proposal.buys[i].price = (market.c_quantize_order_price(self.trading_pair, lower_buy_price)
                                              * (1 - self._bid_order_level_spreads[i] / Decimal("100"))
                                              / (1-self._bid_order_level_spreads[0] / Decimal("100")))
                    continue
                proposal.buys[i].price = market.c_quantize_order_price(self.trading_pair, lower_buy_price) * (1 - self.order_level_spread * i)

        if len(proposal.sells) > 0:
            # Get the top ask price in the market using order_optimization_depth and your sell order volume
            top_ask_price = self._market_info.get_price_for_volume(
                True, self._ask_order_optimization_depth + own_sell_size).result_price
            price_quantum = market.c_get_order_price_quantum(
                self.trading_pair,
                top_ask_price
            )
            # Get the price below the top ask
            price_below_ask = (floor(top_ask_price / price_quantum) - 1) * price_quantum

            # If the price_below_ask is higher than the price suggested by the pricing proposal,
            # increase your price and from there apply the order_level_spread to each order in the next levels
            proposal.sells = sorted(proposal.sells, key = lambda p: p.price)
            higher_sell_price = max(proposal.sells[0].price, price_below_ask)
            for i, proposed in enumerate(proposal.sells):
                if self._split_order_levels_enabled:
                    proposal.sells[i].price = (market.c_quantize_order_price(self.trading_pair, higher_sell_price)
                                               * (1 + self._ask_order_level_spreads[i] / Decimal("100"))
                                               / (1 + self._ask_order_level_spreads[0] / Decimal("100")))
                    continue
                proposal.sells[i].price = market.c_quantize_order_price(self.trading_pair, higher_sell_price) * (1 + self.order_level_spread * i)

    cdef object c_apply_add_transaction_costs(self, object proposal):
        cdef:
            ExchangeBase market = self._market_info.market
        for buy in proposal.buys:
            fee = market.c_get_fee(self.base_asset, self.quote_asset,
                                   self._limit_order_type, TradeType.BUY, buy.size, buy.price)
            price = buy.price * (Decimal(1) - fee.percent)
            buy.price = market.c_quantize_order_price(self.trading_pair, price)
        for sell in proposal.sells:
            fee = market.c_get_fee(self.base_asset, self.quote_asset,
                                   self._limit_order_type, TradeType.SELL, sell.size, sell.price)
            price = sell.price * (Decimal(1) + fee.percent)
            sell.price = market.c_quantize_order_price(self.trading_pair, price)

    cdef c_did_fill_order(self, object order_filled_event):
        cdef:
            str order_id = order_filled_event.order_id
            object market_info = self._sb_order_tracker.get_shadow_market_pair_from_order_id(order_id)
            tuple order_fill_record
            bint is_buy = order_filled_event.trade_type is TradeType.BUY

        if market_info is not None:
            limit_order_record = self._sb_order_tracker.get_shadow_limit_order(order_id)
            order_fill_record = (limit_order_record, order_filled_event)

            # Track the fill price for buy/sell ratio calculation
            if limit_order_record is not None and self._enable_spread_adjustment:
                fill_price = order_filled_event.price
                self._update_avg_prices(is_buy, fill_price)

                # Log the fill for debugging
                if self._logging_options & self.OPTION_LOG_MAKER_ORDER_FILLED:
                    action = "BUY" if is_buy else "SELL"
                    self.log_with_clock(
                        logging.INFO,
                        f"({market_info.trading_pair}) Maker {action} order of "
                        f"{order_filled_event.amount} {market_info.base_asset} filled at {fill_price} "
                        f"(Total {action} prices tracked: {len(self._buy_prices) if is_buy else len(self._sell_prices)})"
                    )
            else:
                # Fallback logging if limit_order_record is None or spread adjustment is disabled
                if self._logging_options & self.OPTION_LOG_MAKER_ORDER_FILLED:
                    action = "BUY" if is_buy else "SELL"
                    self.log_with_clock(
                        logging.INFO,
                        f"({market_info.trading_pair}) Maker {action} order of "
                        f"{order_filled_event.amount} {market_info.base_asset} filled."
                    )

            if self._inventory_cost_price_delegate is not None:
                self._inventory_cost_price_delegate.process_order_fill_event(order_filled_event)

    cdef c_did_cancel_order(self, object order_cancelled_event):
        """
        Handle order cancellation events. Updated for improved order refresh mechanism.
        """
        cdef:
            str order_id = order_cancelled_event.order_id

        self.logger().debug(f"Order {order_id} has been cancelled")

        # For the improved order refresh mechanism, we don't need to set _should_create_orders here
        # The new mechanism handles order creation and cancellation independently
        # The _check_and_cleanup_pending_cancellations() method will handle cleanup

        # Legacy behavior for backward compatibility with other parts of the system
        remaining_orders = self.active_non_hanging_orders
        has_in_flight_cancels = (
            hasattr(self, '_sb_order_tracker') and
            len(self._sb_order_tracker.in_flight_cancels) > 0
        )

        # Only set _should_create_orders if we're not using the improved mechanism
        if not self._orders_pending_cancellation and not self._new_orders_created_successfully:
            if len(remaining_orders) == 0 and not has_in_flight_cancels:
                self.logger().debug("All orders cancelled (legacy mode), enabling order creation")
                self._should_create_orders = True

    cdef c_did_complete_buy_order(self, object order_completed_event):
        cdef:
            str order_id = order_completed_event.order_id
            limit_order_record = self._sb_order_tracker.get_limit_order(self._market_info, order_id)
        if limit_order_record is None:
            return
        active_sell_ids = [x.client_order_id for x in self.active_orders if not x.is_buy]

        if self._hanging_orders_enabled:
            # If the filled order is a hanging order, do nothing
            if order_id in self.hanging_order_ids:
                self.log_with_clock(
                    logging.INFO,
                    f"({self.trading_pair}) Hanging maker buy order {order_id} "
                    f"({limit_order_record.quantity} {limit_order_record.base_currency} @ "
                    f"{limit_order_record.price} {limit_order_record.quote_currency}) has been completely filled."
                )
                self.notify_hb_app_with_timestamp(
                    f"Hanging maker BUY order {limit_order_record.quantity} {limit_order_record.base_currency} @ "
                    f"{limit_order_record.price} {limit_order_record.quote_currency} is filled."
                )
                return

        # delay order creation by filled_order_dalay (in seconds)
        self._create_timestamp = self._current_timestamp + self._filled_order_delay
        self._cancel_timestamp = min(self._cancel_timestamp, self._create_timestamp)

        self._filled_buys_balance += 1
        self._last_own_trade_price = limit_order_record.price

        self.log_with_clock(
            logging.INFO,
            f"({self.trading_pair}) Maker buy order {order_id} "
            f"({limit_order_record.quantity} {limit_order_record.base_currency} @ "
            f"{limit_order_record.price} {limit_order_record.quote_currency}) has been completely filled."
        )
        self.notify_hb_app_with_timestamp(
            f"Maker BUY order {limit_order_record.quantity} {limit_order_record.base_currency} @ "
            f"{limit_order_record.price} {limit_order_record.quote_currency} is filled."
        )

    cdef c_did_complete_sell_order(self, object order_completed_event):
        cdef:
            str order_id = order_completed_event.order_id
            LimitOrder limit_order_record = self._sb_order_tracker.get_limit_order(self._market_info, order_id)
        if limit_order_record is None:
            return
        active_buy_ids = [x.client_order_id for x in self.active_orders if x.is_buy]
        if self._hanging_orders_enabled:
            # If the filled order is a hanging order, do nothing
            if order_id in self.hanging_order_ids:
                self.log_with_clock(
                    logging.INFO,
                    f"({self.trading_pair}) Hanging maker sell order {order_id} "
                    f"({limit_order_record.quantity} {limit_order_record.base_currency} @ "
                    f"{limit_order_record.price} {limit_order_record.quote_currency}) has been completely filled."
                )
                self.notify_hb_app_with_timestamp(
                    f"Hanging maker SELL order {limit_order_record.quantity} {limit_order_record.base_currency} @ "
                    f"{limit_order_record.price} {limit_order_record.quote_currency} is filled."
                )
                return

        # delay order creation by filled_order_dalay (in seconds)
        self._create_timestamp = self._current_timestamp + self._filled_order_delay
        self._cancel_timestamp = min(self._cancel_timestamp, self._create_timestamp)

        self._filled_sells_balance += 1
        self._last_own_trade_price = limit_order_record.price

        self.log_with_clock(
            logging.INFO,
            f"({self.trading_pair}) Maker sell order {order_id} "
            f"({limit_order_record.quantity} {limit_order_record.base_currency} @ "
            f"{limit_order_record.price} {limit_order_record.quote_currency}) has been completely filled."
        )
        self.notify_hb_app_with_timestamp(
            f"Maker SELL order {limit_order_record.quantity} {limit_order_record.base_currency} @ "
            f"{limit_order_record.price} {limit_order_record.quote_currency} is filled."
        )

    cdef bint c_is_within_tolerance(self, list current_prices, list proposal_prices):
        if len(current_prices) != len(proposal_prices):
            return False
        current_prices = sorted(current_prices)
        proposal_prices = sorted(proposal_prices)
        for current, proposal in zip(current_prices, proposal_prices):
            # if spread diff is more than the tolerance or order quantities are different, return false.
            if abs(proposal - current)/current > self._order_refresh_tolerance_pct:
                return False
        return True

    cdef c_cancel_active_orders_on_max_age_limit(self):
        """
        Cancels active non hanging orders if they are older than max age limit
        """
        cdef:
            list active_orders = self.active_non_hanging_orders

        if active_orders and any(order_age(o, self._current_timestamp) > self._max_order_age for o in active_orders):
            old_order_ids = [order.client_order_id for order in active_orders if order_age(order, self._current_timestamp) > self._max_order_age]
            if old_order_ids:
                self.c_cancel_orders_rate_limited(old_order_ids)

    cdef c_cancel_active_orders(self, object proposal):
        """
        Cancels active non hanging orders, checks if the order prices are within tolerance threshold

        Args:
            proposal: The current order proposal (may be None)

        Returns:
            bool: True if orders were canceled, False otherwise
        """
        if self._cancel_timestamp > self._current_timestamp:
            return False

        cdef:
            list active_orders = self.active_non_hanging_orders
            list active_buy_prices = []
            list active_sell_prices = []
            bint orders_canceled = False
            bint to_defer_canceling = False

        if len(active_orders) == 0:
            self.logger().debug("No active non-hanging orders to cancel")
            return False

        # Check if we should defer canceling based on price tolerance
        if proposal is not None and self._order_refresh_tolerance_pct >= 0:
            active_buy_prices = [Decimal(str(o.price)) for o in active_orders if o.is_buy]
            active_sell_prices = [Decimal(str(o.price)) for o in active_orders if not o.is_buy]
            proposal_buys = [buy.price for buy in proposal.buys]
            proposal_sells = [sell.price for sell in proposal.sells]

            if (self.c_is_within_tolerance(active_buy_prices, proposal_buys) and
                self.c_is_within_tolerance(active_sell_prices, proposal_sells)):
                to_defer_canceling = True
                self.logger().debug(
                    f"Deferring order cancellation - prices within tolerance: "
                    f"Buys: {active_buy_prices} vs {proposal_buys}, "
                    f"Sells: {active_sell_prices} vs {proposal_sells}"
                )

        # If we're not deferring cancellation, cancel the orders
        if not to_defer_canceling:
            self._hanging_orders_tracker.update_strategy_orders_with_equivalent_orders()

            # Get all active orders including hanging ones for complete tracking
            all_active_orders = self.active_orders
            active_order_ids = {o.client_order_id for o in all_active_orders}

            # Cancel all active orders with rate limiting for MEXC
            if active_order_ids:
                self.logger().info(f"Canceling {len(active_order_ids)} active orders")
                self.c_cancel_orders_rate_limited(list(active_order_ids))
                orders_canceled = True

        return orders_canceled

        return orders_canceled

    # Cancel Non-Hanging, Active Orders if Spreads are below minimum_spread
    cdef c_cancel_orders_below_min_spread(self):
        cdef:
            list active_orders = self.market_info_to_active_orders.get(self._market_info, [])
            object price = self.get_price()
        active_orders = [order for order in active_orders
                         if order.client_order_id not in self.hanging_order_ids]
        for order in active_orders:
            negation = -1 if order.is_buy else 1
            if (negation * (order.price - price) / price) < self._minimum_spread:
                self.logger().info(f"Order is below minimum spread ({self._minimum_spread})."
                                   f" Canceling Order: ({'Buy' if order.is_buy else 'Sell'}) "
                                   f"ID - {order.client_order_id}")
                self.c_cancel_orders_rate_limited([order.client_order_id])

    cdef bint c_to_create_orders(self, object proposal):
        """
        Determine if we should create new orders.

        Returns:
            bool: True if we should create new orders, False otherwise
        """
        # Get all non-hanging orders that aren't being added to hanging orders
        non_hanging_orders_non_cancelled = [
            o for o in self.active_non_hanging_orders
            if not self._hanging_orders_tracker.is_potential_hanging_order(o)
        ]

        # Check if we have any in-flight cancelations
        has_in_flight_cancels = (
            self._should_wait_order_cancel_confirmation and
            hasattr(self, '_sb_order_tracker') and
            len(self._sb_order_tracker.in_flight_cancels) > 0
        )

        # Determine if we can create new orders
        # For the improved order refresh mechanism, we allow creating orders even when old ones exist
        if self._new_orders_created_successfully:
            # If we already created new orders in this cycle, don't create more
            can_create = False
        elif self._orders_pending_cancellation and not self._new_orders_created_successfully:
            # We're in the middle of an improved refresh cycle - allow creating new orders
            can_create = (
                self._should_create_orders and
                proposal is not None and
                len(proposal.buys) > 0 and
                len(proposal.sells) > 0
            )
        else:
            # Legacy mode - strict checks to prevent duplicate orders
            additional_in_flight_check = (
                hasattr(self, '_sb_order_tracker') and
                hasattr(self._sb_order_tracker, 'in_flight_cancels') and
                len(self._sb_order_tracker.in_flight_cancels) == 0
            )

            can_create = (
                self._should_create_orders and
                not has_in_flight_cancels and
                additional_in_flight_check and  # Extra safety check
                proposal is not None and
                len(proposal.buys) > 0 and
                len(proposal.sells) > 0 and
                len(non_hanging_orders_non_cancelled) == 0 and
                len(self.active_non_hanging_orders) == 0  # Double check no active orders
            )

        # Log detailed status if enabled
        if self._logging_options & self.OPTION_LOG_STATUS_REPORT:
            self.logger().debug(
                f"[Order Creation Check] Can create: {can_create} | "
                f"Should create: {self._should_create_orders} | "
                f"Has proposal: {proposal is not None} | "
                f"Proposal buys/sells: {len(proposal.buys) if proposal else 0}/{len(proposal.sells) if proposal else 0} | "
                f"Active non-hanging orders: {len(non_hanging_orders_non_cancelled)} | "
                f"In-flight cancels: {len(self._sb_order_tracker.in_flight_cancels) if hasattr(self, '_sb_order_tracker') else 'N/A'} | "
                f"Has in-flight cancels: {has_in_flight_cancels} | "
                f"Startup complete: {self._startup_complete}"
            )

            if not can_create and proposal is not None:
                if len(proposal.buys) == 0 or len(proposal.sells) == 0:
                    self.logger().debug(
                        f"[Order Creation Check] Missing proposal sides - "
                        f"Buys: {len(proposal.buys)}, Sells: {len(proposal.sells)}"
                    )

        return can_create

    cdef c_execute_orders_proposal(self, object proposal):
        cdef:
            double expiration_seconds = NaN
            str bid_order_id, ask_order_id
            bint orders_created = False

        if not self._startup_complete:
            self.logger().debug("Skipping order execution: Startup sequence not complete")
            return

        # Check if we should use batch orders for MEXC
        if self._should_use_batch_orders(proposal):
            self.logger().info(f"Using batch orders for {len(proposal.buys)} buys and {len(proposal.sells)} sells")
            self.c_execute_batch_orders_proposal(proposal)
            return

        # Number of pair of orders to track for hanging orders
        number_of_pairs = min((len(proposal.buys), len(proposal.sells))) if self._hanging_orders_enabled else 0

        if len(proposal.buys) > 0:
            if self._logging_options & self.OPTION_LOG_CREATE_ORDER:
                price_quote_str = [f"{buy.size.normalize()} {self.base_asset}, "
                                   f"{buy.price.normalize()} {self.quote_asset}"
                                   for buy in proposal.buys]
                self.logger().info(
                    f"({self.trading_pair}) Creating {len(proposal.buys)} bid orders "
                    f"at (Size, Price): {price_quote_str}"
                )
            for idx, buy in enumerate(proposal.buys):
                bid_order_id = self.c_buy_with_specific_market(
                    self._market_info,
                    buy.size,
                    order_type=self._limit_order_type,
                    price=buy.price,
                    expiration_seconds=expiration_seconds
                )
                orders_created = True
                if idx < number_of_pairs:
                    order = next((o for o in self.active_orders if o.client_order_id == bid_order_id))
                    if order:
                        self._hanging_orders_tracker.add_current_pairs_of_proposal_orders_executed_by_strategy(
                            CreatedPairOfOrders(order, None))
        if len(proposal.sells) > 0:
            if self._logging_options & self.OPTION_LOG_CREATE_ORDER:
                price_quote_str = [f"{sell.size.normalize()} {self.base_asset}, "
                                   f"{sell.price.normalize()} {self.quote_asset}"
                                   for sell in proposal.sells]
                self.logger().info(
                    f"({self.trading_pair}) Creating {len(proposal.sells)} ask "
                    f"orders at (Size, Price): {price_quote_str}"
                )
            for idx, sell in enumerate(proposal.sells):
                ask_order_id = self.c_sell_with_specific_market(
                    self._market_info,
                    sell.size,
                    order_type=self._limit_order_type,
                    price=sell.price,
                    expiration_seconds=expiration_seconds
                )
                orders_created = True
                if idx < number_of_pairs:
                    order = next((o for o in self.active_orders if o.client_order_id == ask_order_id))
                    if order:
                        self._hanging_orders_tracker.current_created_pairs_of_orders[idx].sell_order = order
        if orders_created:
            self.set_timers()

    cdef bint _should_use_batch_orders(self, object proposal):
        """
        Determine if we should use batch orders based on exchange type and order count.
        """
        cdef:
            ExchangeBase market = self._market_info.market
            int total_orders = len(proposal.buys) + len(proposal.sells)

        # Only use batch orders for MEXC exchange and when we have multiple orders
        if hasattr(market, 'name') and market.name == "mexc" and total_orders >= 2:
            # Check if the exchange has batch_order_create method
            if hasattr(market, 'batch_order_create'):
                return True
        return False

    cdef c_execute_batch_orders_proposal(self, object proposal):
        """
        Execute orders using batch order functionality for supported exchanges.
        """
        cdef:
            ExchangeBase market = self._market_info.market
            list buy_orders = []
            list sell_orders = []
            object buy_order, sell_order
            int number_of_pairs = min((len(proposal.buys), len(proposal.sells))) if self._hanging_orders_enabled else 0

        try:
            # Import LimitOrder class
            from hummingbot.core.data_type.limit_order import LimitOrder

            # Create LimitOrder objects for buy orders
            if len(proposal.buys) > 0:
                if self._logging_options & self.OPTION_LOG_CREATE_ORDER:
                    price_quote_str = [f"{buy.size.normalize()} {self.base_asset}, "
                                       f"{buy.price.normalize()} {self.quote_asset}"
                                       for buy in proposal.buys]
                    self.logger().info(
                        f"({self.trading_pair}) Creating {len(proposal.buys)} bid orders via batch "
                        f"at (Size, Price): {price_quote_str}"
                    )

                for idx, buy in enumerate(proposal.buys):
                    buy_order = LimitOrder(
                        client_order_id="",  # Will be generated by batch_order_create
                        trading_pair=self.trading_pair,
                        is_buy=True,
                        base_currency=self.base_asset,
                        quote_currency=self.quote_asset,
                        price=buy.price,
                        quantity=buy.size
                    )
                    buy_orders.append(buy_order)

            # Create LimitOrder objects for sell orders
            if len(proposal.sells) > 0:
                if self._logging_options & self.OPTION_LOG_CREATE_ORDER:
                    price_quote_str = [f"{sell.size.normalize()} {self.base_asset}, "
                                       f"{sell.price.normalize()} {self.quote_asset}"
                                       for sell in proposal.sells]
                    self.logger().info(
                        f"({self.trading_pair}) Creating {len(proposal.sells)} ask orders via batch "
                        f"at (Size, Price): {price_quote_str}"
                    )

                for idx, sell in enumerate(proposal.sells):
                    sell_order = LimitOrder(
                        client_order_id="",  # Will be generated by batch_order_create
                        trading_pair=self.trading_pair,
                        is_buy=False,
                        base_currency=self.base_asset,
                        quote_currency=self.quote_asset,
                        price=sell.price,
                        quantity=sell.size
                    )
                    sell_orders.append(sell_order)

            # Execute batch orders
            all_orders = buy_orders + sell_orders
            if len(all_orders) > 0:
                created_orders = market.batch_order_create(all_orders)

                # Track hanging orders if enabled
                if self._hanging_orders_enabled and number_of_pairs > 0:
                    buy_created = [order for order in created_orders if order.is_buy]
                    sell_created = [order for order in created_orders if not order.is_buy]

                    for idx in range(min(number_of_pairs, len(buy_created), len(sell_created))):
                        if idx < len(buy_created):
                            buy_order = next((o for o in self.active_orders if o.client_order_id == buy_created[idx].client_order_id), None)
                            if buy_order:
                                self._hanging_orders_tracker.current_created_pairs_of_orders[idx].buy_order = buy_order

                        if idx < len(sell_created):
                            sell_order = next((o for o in self.active_orders if o.client_order_id == sell_created[idx].client_order_id), None)
                            if sell_order:
                                self._hanging_orders_tracker.current_created_pairs_of_orders[idx].sell_order = sell_order

                self.set_timers()
                self.logger().info(
                    f"✅ MEXC BATCH ORDERS SUCCESSFUL! ✅\n"
                    f"   Trading pair: {self.trading_pair}\n"
                    f"   Orders created: {len(created_orders)} total\n"
                    f"   Buy orders: {len([o for o in created_orders if o.is_buy])}\n"
                    f"   Sell orders: {len([o for o in created_orders if not o.is_buy])}\n"
                    f"   🎯 Rate limit optimization working perfectly!"
                )

        except Exception as e:
            error_str = str(e).lower()
            if "429" in error_str or "too many requests" in error_str or "rate limit" in error_str:
                self.logger().error(
                    f"🚨 MEXC RATE LIMIT HIT DURING BATCH ORDER CREATION! 🚨\n"
                    f"   Trading pair: {self.trading_pair}\n"
                    f"   Attempted orders: {len(proposal.buys)} buys + {len(proposal.sells)} sells\n"
                    f"   Error: {e}\n"
                    f"   ⚠️  Skipping this cycle to prevent IP ban!\n"
                    f"   💡 Orders will resume automatically when rate limit expires"
                )
                # Don't fallback to individual orders as that would make rate limiting worse
                return
            else:
                self.logger().error(f"Batch order creation failed, falling back to individual orders: {e}")
                # Fallback to individual order creation for non-rate-limit errors
                self.c_execute_individual_orders_proposal(proposal)

    cdef c_execute_individual_orders_proposal(self, object proposal):
        """
        Fallback method to execute orders individually when batch orders fail.
        """
        cdef:
            double expiration_seconds = NaN
            str bid_order_id, ask_order_id
            bint orders_created = False
            int number_of_pairs = min((len(proposal.buys), len(proposal.sells))) if self._hanging_orders_enabled else 0

        if len(proposal.buys) > 0:
            if self._logging_options & self.OPTION_LOG_CREATE_ORDER:
                price_quote_str = [f"{buy.size.normalize()} {self.base_asset}, "
                                   f"{buy.price.normalize()} {self.quote_asset}"
                                   for buy in proposal.buys]
                self.logger().info(
                    f"({self.trading_pair}) Creating {len(proposal.buys)} bid orders (individual fallback) "
                    f"at (Size, Price): {price_quote_str}"
                )
            for idx, buy in enumerate(proposal.buys):
                bid_order_id = self.c_buy_with_specific_market(
                    self._market_info,
                    buy.size,
                    order_type=self._limit_order_type,
                    price=buy.price,
                    expiration_seconds=expiration_seconds
                )
                orders_created = True
                if idx < number_of_pairs:
                    order = next((o for o in self.active_orders if o.client_order_id == bid_order_id))
                    if order:
                        self._hanging_orders_tracker.current_created_pairs_of_orders[idx].buy_order = order

        if len(proposal.sells) > 0:
            if self._logging_options & self.OPTION_LOG_CREATE_ORDER:
                price_quote_str = [f"{sell.size.normalize()} {self.base_asset}, "
                                   f"{sell.price.normalize()} {self.quote_asset}"
                                   for sell in proposal.sells]
                self.logger().info(
                    f"({self.trading_pair}) Creating {len(proposal.sells)} ask orders (individual fallback) "
                    f"at (Size, Price): {price_quote_str}"
                )
            for idx, sell in enumerate(proposal.sells):
                ask_order_id = self.c_sell_with_specific_market(
                    self._market_info,
                    sell.size,
                    order_type=self._limit_order_type,
                    price=sell.price,
                    expiration_seconds=expiration_seconds
                )
                orders_created = True
                if idx < number_of_pairs:
                    order = next((o for o in self.active_orders if o.client_order_id == ask_order_id))
                    if order:
                        self._hanging_orders_tracker.current_created_pairs_of_orders[idx].sell_order = order

        if orders_created:
            self.set_timers()

    cdef c_cancel_orders_rate_limited(self, list order_ids):
        """
        Cancel orders with rate limiting for MEXC exchange.

        :param order_ids: List of order IDs to cancel
        """
        if not order_ids:
            return

        # Check if this is MEXC exchange
        if hasattr(self._market_info.market, 'name') and self._market_info.market.name == "mexc":
            # Use MEXC's rate-limited cancellation
            if hasattr(self._market_info.market, 'queue_orders_for_cancellation'):
                self.logger().info(f"Using MEXC rate-limited cancellation for {len(order_ids)} orders")
                self._market_info.market.queue_orders_for_cancellation(order_ids)
                return

        # Fallback to individual cancellation for other exchanges
        self.logger().debug(f"Using individual cancellation for {len(order_ids)} orders")
        for order_id in order_ids:
            try:
                self.c_cancel_order(self._market_info, order_id)
            except Exception as e:
                self.logger().error(f"Error canceling order {order_id}: {str(e)}")

    cdef set_timers(self):
        cdef double next_cycle = self._current_timestamp + self._order_refresh_time
        if self._create_timestamp <= self._current_timestamp:
            self._create_timestamp = next_cycle
        if self._cancel_timestamp <= self._current_timestamp:
            self._cancel_timestamp = min(self._create_timestamp, next_cycle)

    def _check_and_cleanup_pending_cancellations(self):
        """
        Check if pending order cancellations are complete and clean up the list.
        This is part of the improved order refresh mechanism.
        """
        if not self._orders_pending_cancellation:
            return

        try:
            # Get current active order IDs
            current_active_order_ids = {order.client_order_id for order in self.active_orders}

            # Check which pending cancellations are complete (orders no longer active)
            completed_cancellations = []
            for order_id in self._orders_pending_cancellation:
                if order_id not in current_active_order_ids:
                    completed_cancellations.append(order_id)
                    self.logger().debug(f"Order {order_id} cancellation confirmed")

            # Remove completed cancellations from pending list
            for order_id in completed_cancellations:
                self._orders_pending_cancellation.remove(order_id)

            if completed_cancellations:
                self.logger().info(f"Confirmed cancellation of {len(completed_cancellations)} orders, "
                                 f"{len(self._orders_pending_cancellation)} still pending")

        except Exception as e:
            self.logger().error(f"Error checking pending cancellations: {str(e)}", exc_info=True)
            # On error, clear the list to prevent getting stuck
            self._orders_pending_cancellation = []

    def notify_hb_app(self, msg: str):
        if self._hb_app_notification:
            super().notify_hb_app(msg)

    def get_price_type(self, price_type_str: str) -> PriceType:
        if price_type_str == "mid_price":
            return PriceType.MidPrice
        elif price_type_str == "best_bid":
            return PriceType.BestBid
        elif price_type_str == "best_ask":
            return PriceType.BestAsk
        elif price_type_str == "last_price":
            return PriceType.LastTrade
        elif price_type_str == 'last_own_trade_price':
            return PriceType.LastOwnTrade
        elif price_type_str == 'inventory_cost':
            return PriceType.InventoryCost
        elif price_type_str == "custom":
            return PriceType.Custom
        else:
            raise ValueError(f"Unrecognized price type string {price_type_str}.")
